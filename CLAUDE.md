# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Setup and Installation
```bash
mix setup                    # Install dependencies and setup project
mix deps.get                # Install Elixir dependencies
mix assets.setup            # Install frontend assets (Tailwind, esbuild)
mix assets.build            # Build frontend assets
```

### Running the Application
```bash
mix phx.server              # Start Phoenix server
iex -S mix phx.server       # Start with IEx console
```

### Testing
```bash
mix test                    # Run all tests
mix test test/path/to/specific_test.exs  # Run specific test
```

### Code Quality
```bash
mix credo                   # Run static code analysis
mix format                  # Format code
```

### Asset Development
```bash
mix assets.deploy           # Build and minify assets for production
mix tailwind mqttable       # Build Tailwind CSS
mix esbuild mqttable        # Build JavaScript with esbuild
```

## Architecture Overview

### Core Components

**MQTT Client Management**
- `Mqttable.MqttClient.Manager` - DynamicSupervisor managing MQTT client worker processes
- `Mqttable.MqttClient.Worker` - Individual MQTT client process handling connections
- `Mqttable.MqttClient.Registry` - Process registry for client lookup
- `Mqttable.MqttClient.Subscription` - Manages MQTT subscriptions

**Connection Management**
- `Mqttable.ConnectionSets` - Manages groups of MQTT connections
- `Mqttable.ConnectionSets.Server` - GenServer for connection set state
- `Mqttable.ConnectionSets.Storage` - Persistent storage for connection sets

**Template Engine**
- `Mqttable.Templating.Engine` - Core Liquid template processor with MQTT-specific functions
- `Mqttable.Templating.Functions` - Custom template functions for IoT/MQTT scenarios
- `Mqttable.Templating.Validator` - Template validation logic

**LiveView Components**
- `MqttableWeb.ConnectionsLive` - Main dashboard LiveView
- `MqttableWeb.Live.Components.*` - Reusable UI components for connections, payloads, etc.
- `MqttableWeb.UI.StateManager` - UI state management

**Data Flow**
1. ConnectionSets store connection configurations
2. Manager spawns Worker processes for active connections
3. Workers handle MQTT protocol via emqtt library
4. LiveView subscribes to PubSub for real-time updates
5. Template engine processes payloads with custom functions

### Key Dependencies
- Phoenix LiveView for real-time UI
- emqtt library for MQTT protocol
- Solid (Liquid) for template processing
- SlickGrid for data tables
- Tailwind CSS for styling

### File Structure Patterns
- `lib/mqttable/` - Core business logic
- `lib/mqttable_web/live/` - LiveView modules
- `lib/mqttable_web/live/components/` - Reusable UI components
- `assets/` - Frontend assets (CSS, JS, Svelte)
- `priv/data/` - Application data storage
- `test/` - Test files mirroring lib structure

### State Management
- ETS tables for client state via `ClientRegistry`
- Phoenix PubSub for real-time communication
- GenServer processes for persistent state
- LiveView assigns for UI state