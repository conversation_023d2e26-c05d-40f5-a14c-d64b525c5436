# MQTT Telemetry Integration

This document describes the telemetry-based implementation for MQTT event handling. The system exclusively uses telemetry events to capture and process MQTT packet data for tracing and monitoring, providing a clean and efficient approach to MQTT observability.

## Overview

The MQTT telemetry integration provides event handling through telemetry events emitted by the EMQTT client library. All events are processed through a shared `process_emqtt_report/1` function, ensuring consistent trace message generation and storage.

## Architecture

```
EMQTT Client
    └── Telemetry Events ──→ MqttTelemetryHandler
                                      │
                                      ▼
                            MqttPacketProcessor
                                      │
                                      ▼
                            TraceManager & PubSub
```

## Modules

### `Mqttable.MqttPacketProcessor`
Module containing the core processing logic for MQTT packets. This module:
- Processes different types of MQTT reports (`send_data`, `recv_data`, `websocket_recv_data`, `send_data_failed`)
- Parses MQTT packets using `:emqtt_frame.parse/2`
- Formats and broadcasts trace messages
- Stores messages in the TraceManager

### `Mqttable.MqttTelemetryHandler`
Handles telemetry events emitted by the EMQTT library:
- Attaches to telemetry events: `[:emqtt, :websocket, :recv_data]`, `[:emqtt, :socket, :recv_data]`, `[:emqtt, :socket, :send_data]`, `[:emqtt, :socket, :send_data_failed]`
- Converts telemetry event data to report format
- Delegates processing to `MqttPacketProcessor`

### `Mqttable.MqttTelemetryTracing`
Management module that provides:
- `setup/0` - Start the telemetry handler
- `cleanup/0` - Stop the telemetry handler
- `status/0` - Check status of the telemetry handler
- `subscribe/0` - Subscribe to MQTT trace messages

## Telemetry Events Handled

The telemetry handler processes these EMQTT telemetry events:

| Event | Description | Measurements | Metadata |
|-------|-------------|--------------|----------|
| `[:emqtt, :websocket, :recv_data]` | WebSocket data received | `data_size` | `client_id`, `data` |
| `[:emqtt, :socket, :recv_data]` | Socket data received | `data_size` | `client_id`, `data` |
| `[:emqtt, :socket, :send_data]` | Data sent successfully | `data_size` | `client_id`, `data` |
| `[:emqtt, :socket, :send_data_failed]` | Data send failed | `data_size` | `client_id`, `data`, `reason` |

## Usage

### Starting the Handler
```elixir
# Start the telemetry handler
Mqttable.MqttTelemetryTracing.setup()
```

### Checking Status
```elixir
# Simple status check (returns true/false)
status = Mqttable.MqttTelemetryTracing.status()

# Detailed status information
detailed_status = Mqttable.MqttTelemetryTracing.detailed_status()
# Returns: %{telemetry_handler: true}
```

### Stopping the Handler
```elixir
# Stop the telemetry handler
Mqttable.MqttTelemetryTracing.cleanup()
```

### Subscribing to Trace Messages
```elixir
# Subscribe to MQTT trace messages
Mqttable.MqttTelemetryTracing.subscribe()
```

## Benefits

1. **Lightweight** - Single telemetry-based event capture path
2. **Consistent** - All events processed through the same logic
3. **Reliable** - Telemetry events are emitted directly by the EMQTT library
4. **Future-proof** - Uses the modern telemetry approach for observability

## Application Integration

The application automatically starts the telemetry handler during startup:

```elixir
# In lib/mqttable/application.ex
def start(_type, _args) do
  # ... other children ...

  # Start the MQTT Telemetry Handler
  Mqttable.MqttTelemetryTracing.setup()

  # ... rest of startup ...
end
```

## Error Handling

The telemetry handler includes comprehensive error handling:
- Telemetry handler failures don't affect the main client functionality
- Report processor errors are caught and logged
- Individual event processing failures are isolated

## Performance Considerations

- The telemetry handler is lightweight and processes events asynchronously
- Events are processed through an optimized path
- No significant performance impact on the main MQTT client functionality
- Telemetry events are emitted efficiently by the EMQTT library
- Comprehensive edge case handling prevents crashes and data loss

## Code Quality Improvements

### Edge Case Handling
- **Null Data Validation**: All telemetry events validate that required data exists before processing
- **Type Guards**: Function clauses use guards to ensure proper data types
- **Graceful Degradation**: Invalid data is logged and ignored rather than causing crashes
- **Parse State Validation**: Receive data processing validates parse state exists before parsing

### Error Handling
- **Proper Error Propagation**: Setup and cleanup functions properly handle and propagate errors
- **Already Exists Handling**: Handler startup gracefully handles already-existing handlers
- **Not Found Handling**: Handler shutdown gracefully handles non-existent handlers
- **Comprehensive Logging**: All error conditions are properly logged with context

### Elixir Best Practices
- **Pattern Matching**: Extensive use of pattern matching for control flow
- **Guard Clauses**: Proper use of guards for type and value validation
- **Function Clauses**: Multiple function clauses handle different input scenarios
- **Return Value Consistency**: Consistent return values across all functions
