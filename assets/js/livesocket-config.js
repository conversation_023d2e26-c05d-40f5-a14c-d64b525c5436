// LiveSocket Configuration Module
// This module handles LiveSocket initialization, topbar configuration, and development features

import { Socket } from "phoenix"
import { LiveSocket } from "phoenix_live_view"
import topbar from "../vendor/topbar"

// Initialize and configure LiveSocket
export function initializeLiveSocket(csrfToken, hooks) {
  const liveSocket = new LiveSocket("/live", Socket, {
    longPollFallbackMs: 2500,
    params: {_csrf_token: csrfToken},
    hooks: hooks
  })

  // Show progress bar on live navigation and form submits
  topbar.config({barColors: {0: "#29d"}, shadowColor: "rgba(0, 0, 0, .3)"})
  window.addEventListener("phx:page-loading-start", _info => topbar.show(300))
  window.addEventListener("phx:page-loading-stop", _info => topbar.hide())

  // connect if there are any LiveViews on the page
  liveSocket.connect()

  window.liveSocket = liveSocket

  return liveSocket;
}

// Initialize development features
export function initializeDevelopmentFeatures() {
  // The lines below enable quality of life phoenix_live_reload
  // development features:
  //
  //     1. stream server logs to the browser console
  //     2. click on elements to jump to their definitions in your code editor
  //
  if (process.env.NODE_ENV === "development") {
    window.addEventListener("phx:live_reload:attached", ({detail: reloader}) => {
      // Enable server log streaming to client.
      // Disable with reloader.disableServerLogs()
      reloader.enableServerLogs()

      // Open configured PLUG_EDITOR at file:line of the clicked element's HEEx component
      //
      //   * click with "c" key pressed to open at caller location
      //   * click with "d" key pressed to open at function component definition location
      let keyDown
      window.addEventListener("keydown", e => keyDown = e.key)
      window.addEventListener("keyup", _e => keyDown = null)
      window.addEventListener("click", e => {
        if(keyDown === "c"){
          e.preventDefault()
          e.stopImmediatePropagation()
          reloader.openEditorAtCaller(e.target)
        } else if(keyDown === "d"){
          e.preventDefault()
          e.stopImmediatePropagation()
          reloader.openEditorAtDef(e.target)
        }
      }, true)

      window.liveReloader = reloader
    })
  }
}
