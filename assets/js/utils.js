// Utility Functions Module
// This module contains global utility functions extracted from app.js

// Initialize all global utility functions
export function initializeGlobalUtilities() {
  // Global function to manually restore group states
  window.restoreGroupStates = function() {
    console.log('Global restoreGroupStates called');
    const traceGridElement = document.querySelector('[phx-hook="TraceSlickGrid"]');
    if (traceGridElement && traceGridElement.__phoenixHook) {
      console.log('Found TraceSlickGrid hook, calling restoreGroupStates');
      traceGridElement.__phoenixHook.restoreGroupStates();
    } else {
      console.log('TraceSlickGrid hook not found');
    }
  }

  // Global function to check grouping status
  window.checkGroupingStatus = function() {
    console.log('Global checkGroupingStatus called');
    const traceGridElement = document.querySelector('[phx-hook="TraceSlickGrid"]');
    if (traceGridElement && traceGridElement.__phoenixHook) {
      const hook = traceGridElement.__phoenixHook;
      console.log('=== GROUPING STATUS ===');
      console.log('this.groupingEnabled:', hook.groupingEnabled);
      console.log('this.filters.topic_grouping_enabled:', hook.filters ? hook.filters.topic_grouping_enabled : 'filters not available');
      console.log('data-topic-grouping-enabled:', traceGridElement.dataset.topicGroupingEnabled);
      console.log('Stored group states count:', hook.groupExpandStates ? hook.groupExpandStates.size : 'not available');
      console.log('DataView available:', !!hook.dataView);
      if (hook.dataView) {
        const groups = hook.dataView.getGroups();
        console.log('Current groups count:', groups ? groups.length : 'no groups');
      }
      console.log('======================');
    } else {
      console.log('TraceSlickGrid hook not found');
    }
  }

  // Global function to manually trigger group state capture on change
  window.captureGroupStatesOnChange = function(groupingKey, isExpanded) {
    console.log('Global captureGroupStatesOnChange called');
    const traceGridElement = document.querySelector('[phx-hook="TraceSlickGrid"]');
    if (traceGridElement && traceGridElement.__phoenixHook) {
      console.log('Found TraceSlickGrid hook, calling captureGroupStatesOnChange');
      traceGridElement.__phoenixHook.captureGroupStatesOnChange(groupingKey, isExpanded);
    } else {
      console.log('TraceSlickGrid hook not found');
    }
  }

  // Function to update certificate files visibility based on selected certificate type
  window.updateCertificateFilesVisibility = function() {
    const certificateFilesSection = document.getElementById('certificate-files-section');
    if (!certificateFilesSection) return;

    const selfSignedRadio = document.getElementById('certificate-type-self-signed');
    if (selfSignedRadio && selfSignedRadio.checked) {
      certificateFilesSection.style.display = 'block';
    } else {
      certificateFilesSection.style.display = 'none';
    }
  }
}

// Initialize functionality on DOMContentLoaded
export function initializeDOMContentLoaded() {
  document.addEventListener("DOMContentLoaded", () => {
    // Initialize certificate files visibility
    window.updateCertificateFilesVisibility();
  });
}
