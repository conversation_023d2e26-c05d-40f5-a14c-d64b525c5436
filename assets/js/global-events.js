// Global Event Handlers Module
// This module contains all global Phoenix LiveView event handlers

// Initialize all global event handlers
export function initializeGlobalEventHandlers() {
  // Add custom event handlers
  window.addEventListener("phx:focus_element", (e) => {
    const element = document.getElementById(e.detail.id);
    if (element) {
      setTimeout(() => {
        element.focus();
      }, 50);
    }
  });

  // Global template insertion handler
  window.addEventListener("phx:insert_template_global", (e) => {
    const { target_id, text } = e.detail;
    console.log('Global insert_template_global event received:', { target_id, text });

    // First try to find the exact target_id
    let textarea = document.getElementById(target_id);

    // If not found, try to find any textarea in UnifiedPayloadEditorComponent containers
    if (!textarea) {
      console.log(`Exact textarea ${target_id} not found, searching for UnifiedPayloadEditor textareas...`);

      // Look for any textarea with id starting with "payload-editor-" in the document
      const allTextareas = document.querySelectorAll('textarea[id^="payload-editor-"]');
      console.log(`Found ${allTextareas.length} payload editor textareas:`, Array.from(allTextareas).map(t => t.id));

      // Find the one that's currently visible (in a modal or active component)
      for (const ta of allTextareas) {
        const rect = ta.getBoundingClientRect();
        if (rect.width > 0 && rect.height > 0) {
          textarea = ta;
          console.log(`Using visible textarea: ${ta.id}`);
          break;
        }
      }
    }

    if (!textarea) {
      console.log(`No suitable textarea found, ignoring event`);
      return;
    }

    // Insert text at cursor position
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const currentValue = textarea.value;

    const newValue = currentValue.substring(0, start) + text + currentValue.substring(end);
    textarea.value = newValue;

    const newCursorPos = start + text.length;
    textarea.setSelectionRange(newCursorPos, newCursorPos);
    textarea.focus();

    // Trigger change event to update LiveView
    textarea.dispatchEvent(new Event('input', { bubbles: true }));
    textarea.dispatchEvent(new Event('change', { bubbles: true }));

    console.log(`Successfully inserted template into ${textarea.id}`);
  });

  // Auto-dismiss flash messages
  window.addEventListener("phx:auto_dismiss_flash", (e) => {
    const { kind, delay } = e.detail;
    const flashElement = document.getElementById(`flash-${kind}`);

    if (flashElement) {
      setTimeout(() => {
        // Check if the flash element still exists and is visible
        if (flashElement && flashElement.style.display !== 'none') {
          // Trigger the same action as clicking the close button
          const closeButton = flashElement.querySelector('button[phx-click="lv:clear-flash"]');
          if (closeButton) {
            closeButton.click();
          } else {
            // Fallback: hide the element directly
            flashElement.style.display = 'none';
          }
        }
      }, delay || 5000);
    }
  });

  // Update certificate files visibility when modal is shown
  window.addEventListener("phx:show", (_e) => {
    // Wait a bit for the DOM to be updated
    setTimeout(() => {
      window.updateCertificateFilesVisibility();
    }, 100);
  });
}
