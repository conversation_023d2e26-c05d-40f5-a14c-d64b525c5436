/* Payload Editor Styles */

/* Template syntax highlighting */
.template-syntax {
  background: linear-gradient(90deg, 
    rgba(59, 130, 246, 0.05) 0%, 
    rgba(59, 130, 246, 0.02) 100%);
  border-left: 3px solid rgba(59, 130, 246, 0.3);
  transition: all 0.2s ease-in-out;
}

.template-syntax:focus {
  border-left-color: rgba(59, 130, 246, 0.6);
  box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.3);
}

/* Dark mode template syntax */
[data-theme="dark"] .template-syntax {
  background: linear-gradient(90deg, 
    rgba(139, 92, 246, 0.08) 0%, 
    rgba(139, 92, 246, 0.03) 100%);
  border-left-color: rgba(139, 92, 246, 0.4);
}

[data-theme="dark"] .template-syntax:focus {
  border-left-color: rgba(139, 92, 246, 0.7);
  box-shadow: 0 0 0 1px rgba(139, 92, 246, 0.4);
}

/* Payload editor container */
.payload-editor-container {
  transition: all 0.2s ease-in-out;
}

/* Preview area styling */
.payload-preview {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px dashed #cbd5e1;
  transition: all 0.2s ease-in-out;
}

[data-theme="dark"] .payload-preview {
  background: linear-gradient(135deg, #1e293b 0%, #**********%);
  border-color: #475569;
}

/* Template helper panel */
.template-helper-panel {
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Quick insert buttons */
.quick-insert-btn {
  transition: all 0.15s ease-in-out;
}

.quick-insert-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Format dropdown styling */
.format-dropdown {
  min-width: 120px;
}

/* Textarea enhancements */
.payload-textarea {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  line-height: 1.5;
  tab-size: 2;
}

.payload-textarea::placeholder {
  font-style: italic;
  opacity: 0.6;
}

/* Preview content styling */
.preview-content {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-word;
}

/* Error state styling */
.payload-error {
  color: #ef4444;
  background-color: rgba(239, 68, 68, 0.1);
  border-left-color: #ef4444;
}

[data-theme="dark"] .payload-error {
  color: #f87171;
  background-color: rgba(248, 113, 113, 0.1);
}

/* Success state for valid templates */
.payload-success {
  border-left-color: #10b981;
}

[data-theme="dark"] .payload-success {
  border-left-color: #34d399;
}

/* Loading state */
.payload-loading {
  opacity: 0.7;
  pointer-events: none;
}

.payload-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #e5e7eb;
  border-top-color: #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .template-helper-panel {
    padding: 0.75rem;
  }
  
  .quick-insert-btn {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
  }
  
  .payload-textarea {
    font-size: 0.875rem;
  }
}

/* Focus states */
.payload-editor-container:focus-within {
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
  border-radius: 0.5rem;
}

/* Accessibility improvements */
.template-helper-toggle:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.quick-insert-btn:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Animation for preview updates */
.preview-update {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
