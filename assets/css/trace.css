/* Import SlickGrid styles */
@import "slickgrid/dist/styles/css/slick.grid.css";
@import "slickgrid/dist/styles/css/slick-default-theme.css";

/* Trace component specific styles */

/* Red-themed background for trace section */
.trace-section {
  background-color: rgba(254, 226, 226, 0.7); /* Light red background with transparency */
  border: 1px solid rgba(239, 68, 68, 0.3); /* Red border with transparency */
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

/* Trace section header */
.trace-section-header {
  color: rgb(185, 28, 28); /* Dark red text */
  font-weight: 600;
}

/* Trace message table container - fixed height for exactly 15 rows */
.trace-message-table-container {
  background-color: white;
  border-radius: 0.375rem;
  overflow: hidden;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  /* Fixed height calculation: header (2.5rem) + 15 rows (1.75rem each) + padding */
  height: calc(2.5rem + 18 * 1.75rem + 0.5rem);
  max-height: calc(2.5rem + 18 * 1.75rem + 0.5rem);
  display: flex;
  flex-direction: column;
  /* Transition for smooth height changes */
  transition: height 0.3s ease, max-height 0.3s ease;
}

/* When broker tabs are collapsed, increase table height by 8 rows */
.broker-tabs-collapsed .trace-message-table-container {
  /* Fixed height calculation: header (2.5rem) + 23 rows (1.75rem each) + padding */
  height: calc(2.5rem + 26 * 1.75rem + 0.5rem);
  max-height: calc(2.5rem + 26 * 1.75rem + 0.5rem);
}

/* Table wrapper with scrolling */
.trace-message-table-wrapper {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  /* Disable CSS smooth scrolling - GSAP will handle it */
  scroll-behavior: auto;
  /* Enable hardware acceleration for better performance */
  transform: translateZ(0);
  will-change: scroll-position;
}

/* Table styling */
.trace-message-table-wrapper table {
  min-width: 100%;
  table-layout: fixed;
}

/* Ensure table headers stay visible during scroll */
.trace-message-table-wrapper thead th {
  position: sticky;
  top: 0;
  background-color: white;
  z-index: 10;
  box-shadow: 0 1px 0 0 rgba(0, 0, 0, 0.1);
  height: 2.5rem;
}

/* Table row height consistency */
.trace-message-table-wrapper tbody tr {
  height: 1.75rem;
  min-height: 1.75rem;
  max-height: 1.75rem;
  /* Enable hardware acceleration for smooth animations */
  transform: translateZ(0);
  will-change: transform, opacity, background-color;
  /* Default visible state for existing rows */
  opacity: 1;
  transform: translateY(0);
  transition: background-color 0.3s ease;
}

/* Initial state for new rows that will be animated in */
.trace-message-table-wrapper tbody tr.trace-row-new {
  opacity: 0;
  transform: translateY(-20px);
}

/* Selected message row */
.trace-message-selected {
  background-color: rgba(239, 68, 68, 0.1) !important;
  border-left: 3px solid rgb(239, 68, 68) !important;
}

/* Message type badges */
.badge-publish {
  background-color: rgba(59, 130, 246, 0.1);
  color: rgb(59, 130, 246);
  border-color: rgb(59, 130, 246);
}

.badge-subscribe {
  background-color: rgba(139, 92, 246, 0.1);
  color: rgb(139, 92, 246);
  border-color: rgb(139, 92, 246);
}

.badge-connect {
  background-color: rgba(16, 185, 129, 0.1);
  color: rgb(16, 185, 129);
  border-color: rgb(16, 185, 129);
}



/* Message direction visual indicators - subtle and stable */
.trace-message-row {
  transition: background-color 0.2s ease-out, box-shadow 0.2s ease-out;
}

/* Direction indicators with meaningful colors */
.trace-message-in {
  border-left: 3px solid rgba(34, 197, 94, 0.4);
}

.trace-message-out {
  border-left: 3px solid rgba(234, 179, 8, 0.4);
}

.trace-message-neutral {
  border-left: 3px solid rgba(156, 163, 175, 0.4);
}

/* Only animate on hover for existing rows - very subtle */
.trace-message-row:hover {
  background-color: rgba(59, 130, 246, 0.03) !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* New packet highlight animation - matching infinite_scroll_packets.html */
.trace-message-row.new-packet {
  background: linear-gradient(90deg, #4caf50, transparent);
  animation: highlight 2s ease-out;
}

@keyframes highlight {
  0% {
    background: linear-gradient(90deg, #4caf50, transparent);
  }
  100% {
    background: transparent;
  }
}

/* Legacy support for newly-added class */
.trace-message-row.newly-added {
  animation: new-row-highlight 0.4s ease-out;
}

@keyframes new-row-highlight {
  0% {
    background-color: rgba(59, 130, 246, 0.12);
    box-shadow: 0 2px 6px rgba(59, 130, 246, 0.2);
  }
  100% {
    background-color: transparent;
    box-shadow: none;
  }
}

/* JavaScript-controlled row highlighting - more prominent */
.trace-message-row.js-highlighted {
  background-color: rgba(59, 130, 246, 0.15) !important;
  border-left: 5px solid rgb(59, 130, 246) !important;
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.25);
  transform: translateX(3px);
  transition: all 0.2s ease-out;
}

/* Ensure JS highlight takes precedence over server-side selection */
.trace-message-row.js-highlighted.trace-message-selected {
  background-color: rgba(59, 130, 246, 0.18) !important;
  border-left-color: rgb(59, 130, 246) !important;
}

/* Enhanced pulse animation for clicked rows */
@keyframes row-highlight-pulse {
  0% {
    background-color: rgba(59, 130, 246, 0.25);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.35);
    transform: translateX(4px);
  }
  50% {
    background-color: rgba(59, 130, 246, 0.2);
    box-shadow: 0 5px 18px rgba(59, 130, 246, 0.3);
    transform: translateX(3.5px);
  }
  100% {
    background-color: rgba(59, 130, 246, 0.15);
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.25);
    transform: translateX(3px);
  }
}

/* GSAP animation classes for new rows */
.trace-message-row.gsap-animating {
  /* Ensure GSAP animations take precedence */
  animation: none !important;
}

/* Ensure new packet animations work with GSAP */
.trace-message-row.new-packet.gsap-animating {
  /* Allow both GSAP transforms and CSS background animations */
  animation: highlight 2s ease-out !important;
}

/* Smooth scrolling indicator */
.trace-message-table-wrapper.smooth-scrolling {
  /* Visual indicator during smooth scroll */
  box-shadow: inset 0 0 10px rgba(59, 130, 246, 0.1);
}

/* Remove the duplicate badge animations - they're handled below */

/* Table cell truncation styles - adjusted for better ClientID visibility */
.table-cell-truncate {
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.payload-preview-truncate {
  max-width: 250px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* ClientID specific styling */
.trace-client-id {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Timestamp specific styling - fixed width for exactly 18 characters */
.trace-timestamp {
  font-family: monospace;
  width: 144px; /* 18 characters * 8px per character in monospace */
  min-width: 144px;
  max-width: 144px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: clip; /* Don't use ellipsis since we control the length */
}

/* Loading row animation */
.trace-loading-row {
  animation: loading-pulse 1.5s ease-in-out infinite;
}

@keyframes loading-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Responsive adjustments for smaller screens */
@media (max-width: 768px) {
  .trace-message-table-container {
    /* Adjust height for mobile - fewer visible rows */
    height: calc(2.5rem + 10 * 1.75rem + 0.5rem);
    max-height: calc(2.5rem + 10 * 1.75rem + 0.5rem);
  }

  /* When broker tabs are collapsed on mobile, increase by 6 rows */
  .broker-tabs-collapsed .trace-message-table-container {
    height: calc(2.5rem + 16 * 1.75rem + 0.5rem);
    max-height: calc(2.5rem + 16 * 1.75rem + 0.5rem);
  }

  .table-cell-truncate {
    max-width: 120px;
  }

  .payload-preview-truncate {
    max-width: 150px;
  }

  .trace-client-id {
    max-width: 140px;
  }
}

@media (max-width: 480px) {
  .trace-message-table-container {
    /* Further reduce for very small screens */
    height: calc(2.5rem + 8 * 1.75rem + 0.5rem);
    max-height: calc(2.5rem + 8 * 1.75rem + 0.5rem);
  }

  /* When broker tabs are collapsed on very small screens, increase by 4 rows */
  .broker-tabs-collapsed .trace-message-table-container {
    height: calc(2.5rem + 12 * 1.75rem + 0.5rem);
    max-height: calc(2.5rem + 12 * 1.75rem + 0.5rem);
  }

  .table-cell-truncate {
    max-width: 100px;
  }

  .payload-preview-truncate {
    max-width: 120px;
  }

  .trace-client-id {
    max-width: 110px;
  }
}

/* Smooth scrollbar styling */
.trace-message-table-wrapper::-webkit-scrollbar {
  width: 6px;
}

.trace-message-table-wrapper::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.trace-message-table-wrapper::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 3px;
}

.trace-message-table-wrapper::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5);
}

/* Ensure smooth transitions for interactive elements - very subtle */
.trace-direction,
.trace-message-type,
.trace-client-id {
  transition: transform 0.15s ease-out;
}

/* Subtle hover effects for badges only */
.trace-direction:hover,
.trace-message-type:hover {
  transform: scale(1.02);
}

/* JSON Viewer Geeky Styling */
.geeky-json-viewer {
  --json-viewer-background: #0f1419;
  --json-viewer-color: #e6e6e6;
  --json-viewer-string-color: #86efac;
  --json-viewer-number-color: #60a5fa;
  --json-viewer-boolean-color: #f59e0b;
  --json-viewer-null-color: #ef4444;
  --json-viewer-undefined-color: #8b5cf6;
  --json-viewer-function-color: #06b6d4;
  --json-viewer-rotate-color: #9ca3af;
  --json-viewer-key-color: #fbbf24;
  --json-viewer-url-color: #34d399;
  --json-viewer-date-color: #fb7185;

  background: var(--json-viewer-background);
  color: var(--json-viewer-color);
  font-family: 'JetBrains Mono', 'Fira Code', 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.4;
  border-radius: 6px;
  border: 1px solid #374151;
  padding: 16px;
  max-height: 320px;
  overflow-y: auto;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);

  /* Smooth integration with DaisyUI */
  margin: 0;
  width: 100%;

  /* Custom scrollbar for geeky theme */
  scrollbar-width: thin;
  scrollbar-color: #4b5563 #1f2937;
}

.geeky-json-viewer::-webkit-scrollbar {
  width: 8px;
}

.geeky-json-viewer::-webkit-scrollbar-track {
  background: #1f2937;
  border-radius: 4px;
}

.geeky-json-viewer::-webkit-scrollbar-thumb {
  background: #4b5563;
  border-radius: 4px;
}

.geeky-json-viewer::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

/* JSON Viewer Error Styling */
.json-viewer-error {
  background: #1f2937;
  border: 1px solid #ef4444;
  border-radius: 8px;
  padding: 16px;
  font-family: 'JetBrains Mono', 'Fira Code', 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  max-height: 400px;
  overflow-y: auto;
}

.json-error-header {
  color: #ef4444;
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #374151;
}

.json-raw-content {
  color: #e5e7eb;
  font-size: 12px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-break: break-word;
  margin: 0;
}

/* Ensure JSON viewer integrates well with existing card design */
.json-viewer-container {
  background: transparent;
  border-radius: 0;
  padding: 0;
  width: 100%;
  min-height: 60px;
}

/* Ensure JSON viewer fits within the payload section */
.json-viewer-container .geeky-json-viewer {
  border-radius: 0 0 6px 6px; /* Match the parent container rounding */
  border-top: none; /* Remove top border to blend with header */
}

/* Override default json-viewer styles for better integration */
.geeky-json-viewer json-viewer {
  background: transparent !important;
  font-family: inherit !important;
}

/* Style the expand/collapse buttons */
.geeky-json-viewer .json-viewer__expand-icon {
  color: var(--json-viewer-rotate-color) !important;
  transition: transform 0.2s ease;
}

/* Floating Action Button Styles */
.btn-circle.btn-lg {
  width: 4rem;
  height: 4rem;
}

/* Send Message Modal Positioning */
.send-message-modal {
  position: fixed !important;
  right: 1.5rem !important;
  top: 1.5rem !important;
  margin: 0 !important;
  transform: none !important;
  max-height: calc(100vh - 3rem) !important;
  overflow-y: auto !important;
}

/* Message Detail Modal Positioning */
.message-detail-modal {
  position: fixed !important;
  right: 1.5rem !important;
  top: 1.5rem !important;
  margin: 0 !important;
  transform: none !important;
  max-height: calc(100vh - 3rem) !important;
  overflow-y: auto !important;
}

/* Modal backdrop override - make it transparent and clickable */
.modal.modal-open .modal-backdrop {
  background: transparent;
}

.modal.modal-open .modal-box.send-message-modal {
  background: rgba(255, 255, 255, 0.98);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.modal.modal-open .modal-box.message-detail-modal {
  background: rgba(255, 255, 255, 0.98);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

/* Responsive adjustments for modal */
@media (max-width: 768px) {
  .send-message-modal {
    right: 0.5rem !important;
    top: 0.5rem !important;
    max-width: calc(100vw - 1rem) !important;
    max-height: calc(100vh - 1rem) !important;
  }

  .message-detail-modal {
    right: 0.5rem !important;
    top: 0.5rem !important;
    max-width: calc(100vw - 1rem) !important;
    max-height: calc(100vh - 1rem) !important;
  }
}

/* Ensure floating button doesn't interfere with scrolling */
.main-content-area {
  padding-bottom: 0;
}

.geeky-json-viewer .json-viewer__expand-icon:hover {
  color: var(--json-viewer-key-color) !important;
  transform: scale(1.1);
}

/* Style property keys */
.geeky-json-viewer .json-viewer__key {
  color: var(--json-viewer-key-color) !important;
  font-weight: 500;
}

/* Style different value types */
.geeky-json-viewer .json-viewer__value--string {
  color: var(--json-viewer-string-color) !important;
}

.geeky-json-viewer .json-viewer__value--number {
  color: var(--json-viewer-number-color) !important;
}

.geeky-json-viewer .json-viewer__value--boolean {
  color: var(--json-viewer-boolean-color) !important;
  font-weight: 600;
}

.geeky-json-viewer .json-viewer__value--null {
  color: var(--json-viewer-null-color) !important;
  font-weight: 600;
}

.geeky-json-viewer .json-viewer__value--undefined {
  color: var(--json-viewer-undefined-color) !important;
  font-style: italic;
}

/* Style brackets and punctuation */
.geeky-json-viewer .json-viewer__bracket,
.geeky-json-viewer .json-viewer__comma,
.geeky-json-viewer .json-viewer__colon {
  color: #9ca3af !important;
}

/* Hover effects for interactive elements */
.geeky-json-viewer .json-viewer__key:hover {
  color: #fde047 !important;
  cursor: pointer;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .geeky-json-viewer {
    font-size: 12px;
    padding: 12px;
    max-height: 300px;
  }

  .json-viewer-error {
    padding: 12px;
    max-height: 300px;
  }

  .json-raw-content {
    font-size: 11px;
  }
}

/* SlickGrid Trace Table Styles */

/* Trace grid container */
.trace-grid-container {
  background-color: white;
  border-radius: 0.5rem;
  overflow: hidden; /* Let SlickGrid handle all scrolling */
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  border: 1px solid rgb(229, 231, 235); /* Clean gray border */
  /* Dynamic height calculation - will be set by JavaScript */
  /* Minimum height: header (32px) + 10 rows (32px each) + padding */
  min-height: calc(32px + 10 * 32px + 8px);
  /* Default height for initial render - will be overridden by JavaScript */
  height: calc(32px + 15 * 32px + 8px);
  display: flex;
  flex-direction: column;
  /* Transition for smooth height changes */
  transition: height 0.3s ease, max-height 0.3s ease;
  /* Responsive width - adapt to container size */
  width: 100%;
  min-width: 0; /* Allow shrinking below content width */
}

/* SlickGrid container */
.trace-slick-grid {
  flex: 1;
  overflow: hidden; /* Let SlickGrid handle scrolling internally */
  /* Enable hardware acceleration for better performance */
  transform: translateZ(0);
  will-change: scroll-position;
  /* Optimize touch behavior to reduce scroll-blocking events */
  touch-action: pan-y pinch-zoom;
  /* Clean integration with container - no additional borders */
  background-color: white;
  /* Responsive width - adapt to container size */
  width: 100%;
  max-width: 100%; /* Prevent exceeding container width */
  /* Let SlickGrid handle internal scrolling */
  /* Remove any default spacing */
  margin: 0;
  padding: 0;
  border-spacing: 0;
  border-collapse: collapse;
}

/* SlickGrid grouping styles */
.trace-slick-grid .slick-group {
  background-color: rgb(249, 250, 251); /* Light gray background */
  border-bottom: 1px solid rgb(229, 231, 235); /* Clean gray border */
  font-weight: 600;
  color: rgb(55, 65, 81);
}

.trace-slick-grid .slick-group-title {
  padding: 8px 12px;
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
}

.trace-slick-grid .slick-group-title:hover {
  background-color: rgb(243, 244, 246); /* Slightly darker gray on hover */
}

.trace-slick-grid .slick-group-toggle {
  margin-right: 8px;
  font-size: 12px;
  color: rgb(107, 114, 128);
}

.trace-slick-grid .slick-group-toggle.expanded::before {
  content: "▼";
}

.trace-slick-grid .slick-group-toggle.collapsed::before {
  content: "▶";
}

/* Group level indentation */
.trace-slick-grid .slick-group-title[level='0'] {
  font-weight: bold;
  background-color: rgb(243, 244, 246); /* Slightly darker for top level */
}

.trace-slick-grid .slick-group-title[level='1'] {
  padding-left: 24px;
  font-weight: 600;
}

.trace-slick-grid .slick-group-title[level='2'] {
  padding-left: 36px;
  font-style: italic;
}

/* Override SlickGrid default styles to match modern table theme */
.trace-slick-grid .slick-header {
  background-color: rgb(249, 250, 251); /* Light gray background for distinction */
  border-bottom: 1px solid rgb(229, 231, 235); /* Clean gray border */
  font-weight: 600;
  color: rgb(55, 65, 81);
  height: 2rem; /* Reduced height for more compact appearance */
}

.trace-slick-grid .slick-header-column {
  background-color: rgb(249, 250, 251); /* Consistent light gray background */
  border-right: 1px solid rgb(229, 231, 235); /* Clean gray border */
  padding: 8px 12px; /* Reduced vertical padding for more compact appearance */
  font-size: 0.75rem; /* Smaller font size for better proportion */
  font-weight: 600;
  display: flex;
  align-items: center;
  height: 2rem; /* Reduced height to match header */
  line-height: 1rem; /* Reduced line height */
  color: rgb(75, 85, 99); /* Slightly darker gray for better readability */
  margin: 0; /* Remove any default margins */
  border-top: none; /* Remove top border to prevent gaps */
  border-bottom: none; /* Remove bottom border to prevent gaps */
  border-left: none; /* Remove left border to prevent gaps */
  text-transform: uppercase; /* Make headers more consistent */
  letter-spacing: 0.025em; /* Slight letter spacing for better readability */
}

/* Remove border from last header column to prevent overlap */
.trace-slick-grid .slick-header-column:last-child {
  border-right: none;
}

.trace-slick-grid .slick-header-column:hover {
  background-color: rgb(243, 244, 246); /* Slightly darker gray on hover */
}

/* Row styles - clean modern table design */
.trace-slick-grid .slick-row {
  border-bottom: 1px solid rgb(243, 244, 246); /* Very light gray border */
  transition: background-color 0.2s ease-out, box-shadow 0.2s ease-out;
  height: 2rem; /* Match header height for consistency */
  min-height: 2rem;
  max-height: 2rem;
  margin: 0; /* Remove any default margins */
  border-left: none; /* Remove left border to prevent gaps */
  border-right: none; /* Remove right border to prevent gaps */
}

/* Clean alternating row colors */
.trace-slick-grid .slick-row.odd {
  background-color: white;
}

.trace-slick-grid .slick-row.even {
  background-color: white;
}

/* Clean hover effect */
.trace-slick-grid .slick-row:hover {
  background-color: rgb(249, 250, 251) !important; /* Light gray hover */
  cursor: pointer;
}

/* Selected row styling */
.trace-slick-grid .slick-row.selected {
  background-color: rgba(239, 68, 68, 0.1) !important;
  border-left: 3px solid rgb(239, 68, 68);
}

/* Failed ACK message highlighting */
.trace-slick-grid .slick-row.failed-ack-row {
  background-color: rgba(239, 68, 68, 0.12) !important;
  border-left: 4px solid rgb(239, 68, 68) !important;
  animation: failed-ack-pulse 2s ease-in-out infinite;
}

.trace-slick-grid .slick-row.failed-ack-row:hover {
  background-color: rgba(239, 68, 68, 0.18) !important;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

/* Failed ACK pulse animation */
@keyframes failed-ack-pulse {
  0%, 100% {
    background-color: rgba(239, 68, 68, 0.12);
    border-left-color: rgb(239, 68, 68);
  }
  50% {
    background-color: rgba(239, 68, 68, 0.18);
    border-left-color: rgb(220, 38, 38);
  }
}

/* Cell styling - clean modern design */
.trace-slick-grid .slick-cell {
  padding: 6px 12px; /* Reduced vertical padding to match new row height */
  font-size: 0.8125rem; /* Slightly smaller font for better proportion */
  line-height: 1.125rem; /* Adjusted line height */
  border-right: 1px solid rgb(243, 244, 246); /* Very light gray border */
  display: flex;
  align-items: center;
  height: 2rem; /* Match row height */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  background-color: inherit; /* Inherit row background */
  margin: 0; /* Remove any default margins */
  border-top: none; /* Remove top border to prevent gaps */
  border-bottom: none; /* Remove bottom border to prevent gaps */
  border-left: none; /* Remove left border to prevent gaps */
  /* Allow pointer events for clicking but prevent text selection */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Remove border from last cell to prevent overlap */
.trace-slick-grid .slick-cell:last-child {
  border-right: none;
}

/* Disable cell editing and selection */
.trace-slick-grid .slick-cell.active {
  background: transparent !important;
  border: none !important;
}

.trace-slick-grid .slick-cell.selected {
  background: transparent !important;
  border: none !important;
}

/* Remove focus outline from cells */
.trace-slick-grid .slick-cell:focus {
  outline: none !important;
}

/* Additional gap removal styles */
.trace-slick-grid .slick-viewport {
  margin: 0;
  padding: 0;
  border: none;
  /* Optimize touch behavior for viewport scrolling */
  touch-action: pan-y pinch-zoom;
}

.trace-slick-grid .grid-canvas {
  margin: 0;
  padding: 0;
  border: none;
}

/* Ensure no gaps between table elements */
.trace-slick-grid * {
  box-sizing: border-box;
}

/* Remove any default table spacing */
.trace-slick-grid table {
  border-spacing: 0;
  border-collapse: collapse;
  margin: 0;
  padding: 0;
}

/* Force remove all gaps and spacing */
.trace-slick-grid .slick-header-columns {
  margin: 0;
  padding: 0;
  border: none;
}

.trace-slick-grid .slick-header-columns-left,
.trace-slick-grid .slick-header-columns-right {
  margin: 0;
  padding: 0;
  border: none;
}

/* Ensure no gaps in the grid canvas */
.trace-slick-grid .slick-row {
  border-spacing: 0;
}

/* Remove any potential gaps from resizer elements */
.trace-slick-grid .slick-resizer {
  margin: 0;
  padding: 0;
}

/* Direction indicators with meaningful colors */
.trace-slick-grid .slick-row[data-direction="IN"] {
  border-left: 3px solid rgba(34, 197, 94, 0.4);
}

.trace-slick-grid .slick-row[data-direction="OUT"] {
  border-left: 3px solid rgba(234, 179, 8, 0.4);
}

.trace-slick-grid .slick-row[data-direction=""] {
  border-left: 3px solid rgba(156, 163, 175, 0.4);
}

/* Badge styles within grid cells */
.trace-slick-grid .badge {
  display: inline-flex;
  align-items: center;
  padding: 0.125rem 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 0.375rem;
  border: 1px solid transparent;
}

.trace-slick-grid .badge-ghost {
  background-color: rgba(107, 114, 128, 0.1);
  color: rgb(107, 114, 128);
}

.trace-slick-grid .badge-success {
  background-color: rgba(34, 197, 94, 0.1);
  color: rgb(34, 197, 94);
}

.trace-slick-grid .badge-warning {
  background-color: rgba(245, 158, 11, 0.1);
  color: rgb(245, 158, 11);
}

.trace-slick-grid .badge-info {
  background-color: rgba(59, 130, 246, 0.1);
  color: rgb(59, 130, 246);
}

.trace-slick-grid .badge-accent {
  background-color: rgba(139, 92, 246, 0.1);
  color: rgb(139, 92, 246);
}

.trace-slick-grid .badge-secondary {
  background-color: rgba(107, 114, 128, 0.1);
  color: rgb(107, 114, 128);
}

.trace-slick-grid .badge-primary {
  background-color: rgba(99, 102, 241, 0.1);
  color: rgb(99, 102, 241);
}

/* Viewport styles */
.trace-grid-viewport {
  /* Custom scrollbar styling */
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.3) rgba(0, 0, 0, 0.1);
}

.trace-grid-viewport::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.trace-grid-viewport::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.trace-grid-viewport::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 3px;
}

.trace-grid-viewport::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5);
}

/* Responsive adjustments for SlickGrid */
@media (max-width: 768px) {
  .trace-grid-container {
    /* Minimum height for mobile - 8 rows minimum */
    min-height: calc(32px + 8 * 32px + 8px);
  }

  .trace-slick-grid .slick-cell {
    padding: 4px 8px;
    font-size: 0.8rem;
  }

  .trace-slick-grid .slick-header-column {
    padding: 6px 8px;
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .trace-grid-container {
    /* Minimum height for very small screens - 6 rows minimum */
    min-height: calc(32px + 6 * 32px + 8px);
  }

  .trace-slick-grid .slick-cell {
    padding: 3px 6px;
    font-size: 0.75rem;
  }

  .trace-slick-grid .slick-header-column {
    padding: 4px 6px;
    font-size: 0.75rem;
  }
}
