# Broker Tabs Implementation

## Overview

This document describes the implementation of a VSCode-like tabbed interface for brokers in the MQTTable application. The broker tabs are displayed in a green-themed header section at the top of the main content area.

## Features Implemented

### 1. Tab Display
- Each broker is displayed as a tab in the header area
- Tabs show broker name with clean, readable text
- Active tab has prominent yellow border styling
- Inactive tabs have subtle styling with hover effects

### 2. Visual Design
- Neutral-themed background (`bg-base-200`) for better text readability
- Clean, compact design without color indicators
- Smooth transitions and hover effects
- Responsive design with horizontal scrolling for many tabs

### 3. Drag and Drop Functionality
- Tabs can be reordered by dragging using SortableJS library
- Visual feedback during drag operations (ghost, chosen, drag classes)
- Consistent with existing broker reordering functionality in the sidebar

### 4. Tab Management
- **Add Button**: Primary-styled "+" button matching existing "New broker" button design
- **Close Button**: "×" button on each tab to immediately delete brokers from storage
- **Active State**: Currently active broker highlighted with full yellow border (`border-2 border-yellow-500`)
- **Empty State**: Informative message when no brokers are available
- **Compact Design**: Narrower tabs (`max-w-32`) for better space utilization

### 5. Integration
- Seamlessly integrated with existing broker management system
- Uses same event handlers and state management as sidebar broker menu
- Maintains compatibility with existing functionality

## Files Created/Modified

### New Files
1. `lib/mqttable_web/live/components/broker_tabs_component.ex` - Main component
2. `test/mqttable_web/live/components/broker_tabs_component_test.exs` - Tests
3. `docs/broker_tabs_implementation.md` - This documentation

### Modified Files
1. `lib/mqttable_web/live/connections_live.html.heex` - Added tabs to layout
2. `lib/mqttable_web/live/connections_live.ex` - Added event handlers
3. `assets/css/custom.css` - Added styling for tabs
4. `assets/js/app.js` - Added SortableJS hook for drag-and-drop

## Event Handlers

### `select_broker_tab`
- Triggered when clicking on a broker tab
- Activates the selected broker
- Uses existing `StateManager.handle_toggle_set/2` for consistency

### `close_broker_tab`
- Triggered when clicking the "×" button on a tab
- Disconnects all MQTT connections in the broker
- Immediately removes the broker from storage via `ConnectionSets.update()`
- Handles active broker selection when closing active tab
- Provides user feedback with success/error flash messages

### `reorder_broker_tabs`
- Triggered by SortableJS when tabs are reordered
- Updates broker order in the connection sets
- Persists changes to storage

## CSS Classes

### Container Classes
- `.broker-tabs-container` - Main container with green theme and sticky positioning
- `.broker-tab` - Individual tab styling with transitions
- `.add-broker-btn` - Add button styling with hover effects

### State Classes
- `.sortable-ghost` - Styling during drag operation
- `.sortable-chosen` - Styling for selected item during drag
- `.sortable-drag` - Styling for item being dragged

## JavaScript Integration

### BrokerTabsSortable Hook
- Initializes SortableJS on the tabs container
- Handles drag-and-drop events
- Filters out buttons from dragging
- Sends reorder events to Phoenix LiveView

## Usage

The broker tabs automatically appear at the top of the content area when brokers are available. Users can:

1. **Switch Brokers**: Click on any tab to activate that broker
2. **Reorder Brokers**: Drag tabs to reorder them
3. **Add Brokers**: Click the "+" button to create a new broker
4. **Close Brokers**: Click the "×" button to close a broker
5. **View Status**: Active broker is highlighted with yellow border

## Testing

Comprehensive tests cover:
- Empty state rendering
- Broker tab rendering with correct styling
- Active broker highlighting
- Add button presence
- Sortable hook integration
- Close button functionality

## Future Enhancements

Potential improvements could include:
- Tab context menus (right-click)
- Tab tooltips with broker details
- Keyboard navigation support
- Tab overflow handling for many brokers
- Tab grouping or categorization
