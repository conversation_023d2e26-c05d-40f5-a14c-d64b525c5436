defmodule MqttableWeb.SubscriptionModalComponentTest do
  use MqttableWeb.ConnCase, async: true
  import Phoenix.LiveViewTest

  alias MqttableWeb.SubscriptionModalComponent

  describe "subscription modal component" do
    test "renders with MQTT 5.0 options for MQTT 5.0 client" do
      assigns = %{
        id: "test-modal",
        show_subscription_modal: true,
        active_connection_set: %{
          connections: [
            %{client_id: "client1", mqtt_version: "5.0", name: "Test Client", status: "connected"}
          ]
        },
        pre_selected_client_id: "client1",
        edit_mode: false,
        client_id: nil,
        topic: nil,
        qos: 0,
        nl: false,
        rap: false,
        rh: 0,
        sub_id: nil,
        index: nil
      }

      html = render_component(SubscriptionModalComponent, assigns)

      # Should show MQTT 5.0 specific options
      assert html =~ "No Local"
      assert html =~ "Retain as Published"
      assert html =~ "Retain Handling"
      assert html =~ "Subscription Identifier"
    end

    test "hides MQTT 5.0 options for MQTT 3.1.1 client" do
      assigns = %{
        id: "test-modal-2",
        show_subscription_modal: true,
        active_connection_set: %{
          connections: [
            %{
              client_id: "client1",
              mqtt_version: "3.1.1",
              name: "Test Client",
              status: "connected"
            }
          ]
        },
        pre_selected_client_id: "client1",
        edit_mode: false,
        client_id: nil,
        topic: nil,
        qos: 0,
        nl: false,
        rap: false,
        rh: 0,
        sub_id: nil,
        index: nil
      }

      html = render_component(SubscriptionModalComponent, assigns)

      # Should not show MQTT 5.0 specific options
      refute html =~ "No Local"
      refute html =~ "Retain as Published"
      refute html =~ "Retain Handling"
      refute html =~ "Subscription Identifier"

      # Should still show basic options
      assert html =~ "Topic"
      assert html =~ "QoS"
    end

    test "shows MQTT version in client selection dropdown" do
      assigns = %{
        id: "test-modal-3",
        show_subscription_modal: true,
        active_connection_set: %{
          connections: [
            %{
              client_id: "client1",
              mqtt_version: "5.0",
              name: "MQTT5 Client",
              status: "connected"
            },
            %{
              client_id: "client2",
              mqtt_version: "3.1.1",
              name: "MQTT3 Client",
              status: "connected"
            }
          ]
        },
        pre_selected_client_id: nil,
        edit_mode: false,
        client_id: nil,
        topic: nil,
        qos: 0,
        nl: false,
        rap: false,
        rh: 0,
        sub_id: nil,
        index: nil
      }

      html = render_component(SubscriptionModalComponent, assigns)

      # Should show MQTT version in client options
      assert html =~ "MQTT5 Client (client1) - MQTT 5.0"
      assert html =~ "MQTT3 Client (client2) - MQTT 3.1.1"
    end
  end
end
