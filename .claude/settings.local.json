{"permissions": {"allow": ["mcp__Context7__resolve-library-id", "mcp__Context7__get-library-docs", "WebFetch(domain:context7.com)", "Bash(mix compile)", "Bash(mix phx.server:*)", "mcp__tideware__project_eval", "Bash(mix compile:*)", "mcp__tideware__get_logs", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(mix credo:*)"]}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["Context7", "tideware", "browsermcp"]}