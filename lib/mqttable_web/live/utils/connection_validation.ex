defmodule MqttableWeb.Utils.ConnectionValidation do
  @moduledoc """
  Shared validation functions for connection forms.
  This module provides common validation logic for both new and edit connection modals.
  """

  @doc """
  Validates connection parameters for uniqueness within a broker.

  ## Parameters

  - `connection_params`: Map containing connection parameters from the form
  - `existing_connections`: List of existing connections in the current broker
  - `exclude_client_id`: Optional client_id to exclude from validation (for edit mode)

  ## Returns

  - `{:ok, %{}}`: When validation passes with no errors
  - `{:error, %{field => error_message}}`: When validation fails with error details
  """
  @spec validate_connection_uniqueness(map(), list(), String.t() | nil) ::
          {:ok, map()} | {:error, map()}
  def validate_connection_uniqueness(
        connection_params,
        existing_connections,
        exclude_client_id \\ nil
      ) do
    name = Map.get(connection_params, "name", "")
    client_id = Map.get(connection_params, "client_id", "")
    receive_maximum = Map.get(connection_params, "receive_maximum", "0")

    # Filter out the connection being edited if exclude_client_id is provided
    filtered_connections =
      if exclude_client_id do
        Enum.reject(existing_connections, fn conn -> conn.client_id == exclude_client_id end)
      else
        existing_connections
      end

    errors = %{}

    # Validate name uniqueness
    errors =
      if name != "" && name_exists?(filtered_connections, name) do
        Map.put(errors, :name, "Connection name '#{name}' already exists in this broker")
      else
        errors
      end

    # Validate client_id uniqueness
    errors =
      if client_id != "" && client_id_exists?(filtered_connections, client_id) do
        Map.put(errors, :client_id, "Client ID '#{client_id}' already exists in this broker")
      else
        errors
      end

    # Validate required fields
    errors =
      if name == "" do
        Map.put(errors, :name, "Name is required")
      else
        errors
      end

    errors =
      if client_id == "" do
        Map.put(errors, :client_id, "Client ID is required")
      else
        errors
      end

    errors =
      if receive_maximum == "" do
        errors
      else
        case Integer.parse(receive_maximum) do
          {int_val, ""} when int_val < 0 ->
            Map.put(errors, :receive_maximum, "Receive Maximum must be a positive number")

          {int_val, ""} when int_val > 65535 ->
            Map.put(errors, :receive_maximum, "Receive Maximum must be less than 65535")

          _ ->
            Map.put(errors, :receive_maximum, "Receive Maximum must be a number")
        end
      end

    if map_size(errors) == 0 do
      {:ok, %{}}
    else
      {:error, errors}
    end
  end

  @doc """
  Checks if a connection name already exists in the given connections list.
  """
  @spec name_exists?(list(), String.t()) :: boolean()
  def name_exists?(connections, name) when is_list(connections) and is_binary(name) do
    Enum.any?(connections, fn conn -> conn.name == name end)
  end

  @doc """
  Checks if a client ID already exists in the given connections list.
  """
  @spec client_id_exists?(list(), String.t()) :: boolean()
  def client_id_exists?(connections, client_id)
      when is_list(connections) and is_binary(client_id) do
    Enum.any?(connections, fn conn -> conn.client_id == client_id end)
  end

  @doc """
  Validates broker name uniqueness.

  ## Parameters

  - `broker_name`: The broker name to validate
  - `existing_brokers`: List of existing brokers/connection sets
  - `exclude_broker_name`: Optional broker name to exclude from validation (for edit mode)

  ## Returns

  - `{:ok, nil}`: When validation passes
  - `{:error, error_message}`: When validation fails
  """
  @spec validate_broker_name_uniqueness(String.t(), list(), String.t() | nil) ::
          {:ok, nil} | {:error, String.t()}
  def validate_broker_name_uniqueness(broker_name, existing_brokers, exclude_broker_name \\ nil) do
    # Filter out the broker being edited if exclude_broker_name is provided
    filtered_brokers =
      if exclude_broker_name do
        Enum.reject(existing_brokers, fn broker -> broker.name == exclude_broker_name end)
      else
        existing_brokers
      end

    cond do
      broker_name == "" ->
        {:error, "Broker name is required"}

      broker_name_exists?(filtered_brokers, broker_name) ->
        {:error, "Broker name '#{broker_name}' already exists"}

      true ->
        {:ok, nil}
    end
  end

  @doc """
  Checks if a broker name already exists in the given brokers list.
  """
  @spec broker_name_exists?(list(), String.t()) :: boolean()
  def broker_name_exists?(brokers, name) when is_list(brokers) and is_binary(name) do
    Enum.any?(brokers, fn broker -> broker.name == name end)
  end

  @doc """
  Validates a single field for uniqueness.

  ## Parameters

  - `field`: The field to validate (:name or :client_id)
  - `value`: The value to check
  - `existing_connections`: List of existing connections
  - `exclude_client_id`: Optional client_id to exclude from validation

  ## Returns

  - `{:ok, nil}`: When validation passes
  - `{:error, error_message}`: When validation fails
  """
  @spec validate_field_uniqueness(atom(), String.t(), list(), String.t() | nil) ::
          {:ok, nil} | {:error, String.t()}
  def validate_field_uniqueness(field, value, existing_connections, exclude_client_id \\ nil) do
    # Filter out the connection being edited if exclude_client_id is provided
    filtered_connections =
      if exclude_client_id do
        Enum.reject(existing_connections, fn conn -> conn.client_id == exclude_client_id end)
      else
        existing_connections
      end

    case field do
      :name ->
        if value != "" && name_exists?(filtered_connections, value) do
          {:error, "Connection name '#{value}' already exists in this broker"}
        else
          {:ok, nil}
        end

      :client_id ->
        if value != "" && client_id_exists?(filtered_connections, value) do
          {:error, "Client ID '#{value}' already exists in this broker"}
        else
          {:ok, nil}
        end

      _ ->
        {:ok, nil}
    end
  end
end
