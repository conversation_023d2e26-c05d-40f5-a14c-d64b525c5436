defmodule MqttableWeb.SettingsModalComponent do
  @moduledoc """
  LiveComponent for the application settings modal.
  Provides configuration options for time zone and trace manager settings.
  """
  use MqttableWeb, :live_component
  require Logger

  alias Mqttable.Settings

  @impl true
  def update(assigns, socket) do
    # Only initialize settings on first load or when explicitly requested
    # This prevents external updates from resetting the modal state
    should_reload_settings =
      not Map.has_key?(socket.assigns, :current_settings) or
        Map.get(assigns, :force_reload, false)

    socket =
      if should_reload_settings do
        # Load current settings
        current_settings = Settings.get_all()

        socket
        |> assign(assigns)
        |> assign(:current_settings, current_settings)
        |> assign(:time_zone, Map.get(current_settings, :time_zone, "UTC"))
        |> assign(
          :max_messages_per_broker,
          Map.get(current_settings, :max_messages_per_broker, 3000)
        )
        |> assign(:time_zone_options, Settings.get_time_zone_options())
        |> assign(:form_errors, %{})
        |> assign(:factory_reset_step, :none)
      else
        # Keep existing state, only update the assigns that don't affect form state
        socket
        |> assign(Map.take(assigns, [:id]))
      end

    {:ok, socket}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <h3 class="text-lg font-bold mb-4">
        <.icon name="hero-cog-6-tooth" class="size-5 mr-2 inline" /> Application Settings
      </h3>

      <form phx-submit="save_settings" phx-target={@myself} class="space-y-6">
        <!-- Time Zone Setting -->
        <div class="form-control">
          <label class="label">
            <span class="label-text font-medium">
              <.icon name="hero-clock" class="size-4 mr-2 inline" /> Time Zone
            </span>
          </label>
          <select
            name="time_zone"
            class="select select-bordered w-full"
            value={@time_zone}
            phx-change="update_time_zone"
            phx-target={@myself}
          >
            <%= for option <- @time_zone_options do %>
              <option value={option.value} selected={option.value == @time_zone}>
                {option.label}
              </option>
            <% end %>
          </select>
          <label class="label">
            <span class="label-text-alt text-base-content/70">
              This affects how timestamps are displayed throughout the application.
            </span>
          </label>
          <%= if Map.has_key?(@form_errors, :time_zone) do %>
            <label class="label">
              <span class="label-text-alt text-error">{@form_errors.time_zone}</span>
            </label>
          <% end %>
        </div>
        
    <!-- Max Messages Per Broker Setting -->
        <div class="form-control">
          <label class="label">
            <span class="label-text font-medium">
              <.icon name="hero-document-text" class="size-4 mr-2 inline" />
              Maximum Messages Per Broker
            </span>
          </label>
          <input
            type="number"
            name="max_messages_per_broker"
            class="input input-bordered w-full"
            value={@max_messages_per_broker}
            min="100"
            max="10000"
            step="100"
            phx-change="update_max_messages"
            phx-target={@myself}
          />
          <label class="label">
            <span class="label-text-alt text-base-content/70">
              Controls how many trace messages are kept in memory per broker. Higher values use more memory but keep more history.
            </span>
          </label>
          <%= if Map.has_key?(@form_errors, :max_messages_per_broker) do %>
            <label class="label">
              <span class="label-text-alt text-error">{@form_errors.max_messages_per_broker}</span>
            </label>
          <% end %>
        </div>
        
    <!-- Current Settings Display -->
        <div class="bg-base-200 rounded-lg p-4">
          <h4 class="font-medium mb-2">Current Settings</h4>
          <div class="text-sm space-y-1">
            <div>
              <span class="font-medium">Time Zone:</span>
              <span class="ml-2">{@time_zone}</span>
            </div>
            <div>
              <span class="font-medium">Max Messages:</span>
              <span class="ml-2">{@max_messages_per_broker}</span>
            </div>
          </div>
        </div>
        
    <!-- Danger Zone -->
        <div class="bg-error/10 border border-error/20 rounded-lg p-4">
          <h4 class="font-medium text-error mb-2">
            <.icon name="hero-exclamation-triangle" class="size-4 mr-2 inline" /> Danger Zone
          </h4>
          <p class="text-sm text-base-content/70 mb-3">
            Factory reset will permanently delete all broker configurations, trace messages, and reset all settings to defaults.
          </p>
          <button
            type="button"
            class="btn btn-error btn-sm"
            phx-click="start_factory_reset"
            phx-target={@myself}
          >
            <.icon name="hero-trash" class="size-4 mr-2" /> Factory Reset
          </button>
        </div>
        
    <!-- Action Buttons -->
        <div class="modal-action">
          <button
            type="button"
            class="btn btn-ghost"
            phx-click="close_settings_modal"
            phx-target={@myself}
          >
            Close
          </button>
          <button
            type="button"
            class="btn btn-outline"
            phx-click="reset_to_defaults"
            phx-target={@myself}
          >
            Reset to Defaults
          </button>
          <button type="submit" class="btn btn-primary">
            <.icon name="hero-check" class="size-4 mr-2" /> Save Settings
          </button>
        </div>
      </form>
      
    <!-- Factory Reset Confirmation Dialogs -->
      <%= if @factory_reset_step == :first_confirmation do %>
        <div class="modal modal-open">
          <div class="modal-box">
            <h3 class="font-bold text-lg text-error">
              <.icon name="hero-exclamation-triangle" class="size-5 mr-2 inline" />
              Factory Reset Warning
            </h3>
            <p class="py-4">
              This action will permanently delete:
            </p>
            <ul class="list-disc list-inside space-y-1 text-sm mb-4">
              <li>All broker configurations</li>
              <li>All MQTT connections</li>
              <li>All trace messages and history</li>
              <li>All application settings</li>
            </ul>
            <p class="text-error font-medium">
              This action cannot be undone!
            </p>
            <div class="modal-action">
              <button
                type="button"
                class="btn btn-ghost"
                phx-click="cancel_factory_reset"
                phx-target={@myself}
              >
                Cancel
              </button>
              <button
                type="button"
                class="btn btn-error"
                phx-click="confirm_factory_reset_first"
                phx-target={@myself}
              >
                Continue
              </button>
            </div>
          </div>
        </div>
      <% end %>

      <%= if @factory_reset_step == :second_confirmation do %>
        <div class="modal modal-open">
          <div class="modal-box">
            <h3 class="font-bold text-lg text-error">
              <.icon name="hero-exclamation-triangle" class="size-5 mr-2 inline" /> Final Confirmation
            </h3>
            <p class="py-4 text-error font-medium">
              Are you absolutely sure you want to perform a factory reset?
            </p>
            <p class="text-sm mb-4">
              This will immediately disconnect all MQTT connections and permanently delete all data.
            </p>
            <div class="modal-action">
              <button
                type="button"
                class="btn btn-ghost"
                phx-click="cancel_factory_reset"
                phx-target={@myself}
              >
                Cancel
              </button>
              <button
                type="button"
                class="btn btn-error"
                phx-click="execute_factory_reset"
                phx-target={@myself}
              >
                <.icon name="hero-trash" class="size-4 mr-2" /> Yes, Reset Everything
              </button>
            </div>
          </div>
        </div>
      <% end %>
    </div>
    """
  end

  @impl true
  def handle_event("update_time_zone", %{"time_zone" => time_zone}, socket) do
    # Validate time zone
    errors = validate_time_zone(time_zone, socket.assigns.form_errors)

    socket =
      socket
      |> assign(:time_zone, time_zone)
      |> assign(:form_errors, errors)

    {:noreply, socket}
  end

  @impl true
  def handle_event(
        "update_max_messages",
        %{"max_messages_per_broker" => max_messages_str},
        socket
      ) do
    # Validate and convert max messages
    {max_messages, errors} = validate_max_messages(max_messages_str, socket.assigns.form_errors)

    socket =
      socket
      |> assign(:max_messages_per_broker, max_messages)
      |> assign(:form_errors, errors)

    {:noreply, socket}
  end

  @impl true
  def handle_event("save_settings", _params, socket) do
    # Final validation
    time_zone_errors = validate_time_zone(socket.assigns.time_zone, %{})

    {max_messages, max_messages_errors} =
      validate_max_messages(to_string(socket.assigns.max_messages_per_broker), %{})

    all_errors = Map.merge(time_zone_errors, max_messages_errors)

    if map_size(all_errors) == 0 do
      # Save settings
      new_settings = %{
        time_zone: socket.assigns.time_zone,
        max_messages_per_broker: max_messages
      }

      Settings.update(new_settings)

      # Close modal and redirect to refresh the page with new settings
      send(self(), {:settings_saved_redirect})

      {:noreply, socket}
    else
      # Show validation errors
      socket = assign(socket, :form_errors, all_errors)
      {:noreply, socket}
    end
  end

  @impl true
  def handle_event("reset_to_defaults", _params, socket) do
    defaults = Settings.default_settings()

    # Save the default settings immediately
    Settings.update(defaults)

    # Close modal and redirect to refresh the page with default settings
    send(self(), {:settings_saved_redirect})

    {:noreply, socket}
  end

  @impl true
  def handle_event("close_settings_modal", _params, socket) do
    send(self(), {:close_settings_modal})
    {:noreply, socket}
  end

  @impl true
  def handle_event("start_factory_reset", _params, socket) do
    socket = assign(socket, :factory_reset_step, :first_confirmation)
    {:noreply, socket}
  end

  @impl true
  def handle_event("cancel_factory_reset", _params, socket) do
    socket = assign(socket, :factory_reset_step, :none)
    {:noreply, socket}
  end

  @impl true
  def handle_event("confirm_factory_reset_first", _params, socket) do
    socket = assign(socket, :factory_reset_step, :second_confirmation)
    {:noreply, socket}
  end

  @impl true
  def handle_event("execute_factory_reset", _params, socket) do
    # Reset all connection sets and related data
    Mqttable.ConnectionSets.reset_all_data()

    # Reset all settings to factory defaults
    Mqttable.Settings.reset_to_factory_defaults()

    # Send message to parent LiveView to update state immediately
    send(self(), {:factory_reset_completed})

    {:noreply, socket}
  end

  # Private validation functions

  defp validate_time_zone(time_zone, current_errors) do
    time_zone_options = Settings.get_time_zone_options()
    valid_zones = Enum.map(time_zone_options, & &1.value)

    if time_zone in valid_zones do
      Map.delete(current_errors, :time_zone)
    else
      Map.put(current_errors, :time_zone, "Invalid time zone selected")
    end
  end

  defp validate_max_messages(max_messages_str, current_errors) do
    case Integer.parse(max_messages_str) do
      {max_messages, ""} when max_messages >= 100 and max_messages <= 10000 ->
        {max_messages, Map.delete(current_errors, :max_messages_per_broker)}

      {_max_messages, ""} ->
        {3000,
         Map.put(current_errors, :max_messages_per_broker, "Must be between 100 and 10,000")}

      _error ->
        {3000, Map.put(current_errors, :max_messages_per_broker, "Must be a valid number")}
    end
  end
end
