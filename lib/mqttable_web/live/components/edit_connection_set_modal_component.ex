defmodule MqttableWeb.EditConnectionSetModalComponent do
  @moduledoc """
  LiveComponent for rendering the edit connection set modal with variables.
  This component is used to edit an existing connection set with variables.
  """
  use MqttableWeb, :live_component
  alias MqttableWeb.ConnectionSetFormComponent
  alias MqttableWeb.Utils.ConnectionValidation
  alias Mqttable.Uploads.CertificateHandler

  @impl true
  def render(assigns) do
    assigns = assign(assigns, :is_edit, true)

    ~H"""
    <div>
      <h3 class="text-xl font-semibold mb-4 text-center">Edit Broker</h3>
      <.form
        for={%{}}
        as={:connection_set}
        phx-submit="update_connection_set"
        phx-change="validate"
        phx-target={@myself}
      >
        <input type="hidden" name="old_name" value={@original_name} />
        <div class="border border-gray-300 rounded-lg p-2 bg-white">
          <div class="space-y-2">
            <div class="grid grid-cols-1 gap-4">
              <ConnectionSetFormComponent.connection_set_form_fields
                edit_connection_set={@edit_connection_set}
                uploads={@uploads}
                myself={@myself}
              />
            </div>

            <ConnectionSetFormComponent.variables_table
              edit_connection_set={@edit_connection_set}
              myself={@myself}
              is_edit={@is_edit}
            />

            <div class="flex justify-between space-x-2 pt-3">
              <div>
                <button
                  type="button"
                  phx-click="delete_connection_set"
                  phx-target={@myself}
                  phx-value-name={Map.get(@edit_connection_set, :name, "")}
                  class="btn btn-primary"
                >
                  <.icon name="hero-trash" class="size-4 mr-1" /> Delete
                </button>
              </div>
              <div class="flex space-x-2">
                <button
                  type="button"
                  phx-click="close_modal"
                  phx-target="#connection-set-modal"
                  class="btn"
                >
                  Cancel
                </button>
                <button type="submit" class="btn btn-primary">Done</button>
              </div>
            </div>
          </div>
        </div>
      </.form>
    </div>
    """
  end

  @impl true
  def update(assigns, socket) do
    # Store the original name of the connection set
    # This is important for tracking the connection set when it's renamed
    original_name =
      if assigns.edit_connection_set, do: assigns.edit_connection_set.name, else: nil

    # Ensure connection_sets is available in the socket
    connection_sets = Map.get(assigns, :connection_sets, [])

    socket =
      socket
      |> assign(assigns)
      |> assign(:original_name, original_name)
      |> assign(:connection_sets, connection_sets)
      |> allow_upload(:ca_file, ConnectionSetFormComponent.upload_options())
      |> allow_upload(:client_cert_file, ConnectionSetFormComponent.upload_options())
      |> allow_upload(:client_key_file, ConnectionSetFormComponent.upload_options())

    {:ok, socket}
  end

  def handle_event("validate", %{"connection_set" => connection_set_params}, socket) do
    # Get the broker name from params
    broker_name = Map.get(connection_set_params, "name", "")

    # Get existing brokers
    existing_brokers = socket.assigns.connection_sets

    # Get the original name to exclude from validation
    original_name = socket.assigns.original_name

    # Validate broker name uniqueness (excluding current broker)
    case ConnectionValidation.validate_broker_name_uniqueness(
           broker_name,
           existing_brokers,
           original_name
         ) do
      {:ok, _} ->
        # Clear any existing name error
        updated_set = Map.put(socket.assigns.edit_connection_set, :name_error, nil)
        {:noreply, assign(socket, :edit_connection_set, updated_set)}

      {:error, error_message} ->
        # Set the name error
        updated_set = Map.put(socket.assigns.edit_connection_set, :name_error, error_message)
        {:noreply, assign(socket, :edit_connection_set, updated_set)}
    end
  end

  @impl true
  def handle_event("validate", _params, socket) do
    # This event is triggered when a file is selected
    # The socket is already configured with allow_upload in the update/2 function
    # We just need to return the socket to allow the upload to proceed
    {:noreply, socket}
  end

  @impl true
  def handle_event("validate_name", params, socket) do
    ConnectionSetFormComponent.handle_validate_name(params, socket)
  end

  @impl true
  def handle_event("protocol_changed", params, socket) do
    ConnectionSetFormComponent.handle_protocol_changed(params, socket)
  end

  @impl true
  def handle_event("cancel-upload", %{"ref" => ref, "upload" => upload_name}, socket) do
    {:noreply, cancel_upload(socket, String.to_existing_atom(upload_name), ref)}
  end

  @impl true
  def handle_event("add_variable_row", params, socket) do
    ConnectionSetFormComponent.handle_add_variable_row(params, socket)
  end

  @impl true
  def handle_event("delete_variable", params, socket) do
    ConnectionSetFormComponent.handle_delete_variable(params, socket)
  end

  @impl true
  def handle_event("clear_variables", params, socket) do
    ConnectionSetFormComponent.handle_clear_variables(params, socket)
  end

  @impl true
  def handle_event("update_variable", params, socket) do
    ConnectionSetFormComponent.handle_update_variable(params, socket)
  end

  @impl true
  def handle_event("update_form_field", params, socket) do
    ConnectionSetFormComponent.handle_update_form_field(params, socket)
  end

  @impl true
  def handle_event("delete_connection_set", %{"name" => name}, socket) do
    # Send the event to the parent LiveView
    send(self(), {:delete_connection_set, %{"name" => name}})
    {:noreply, socket}
  end

  @impl true
  def handle_event("update_connection_set", params, socket) do
    # Check if there's a name error
    if Map.get(socket.assigns.edit_connection_set, :name_error) do
      # If there's an error, don't proceed with the update
      {:noreply, socket}
    else
      # Process uploaded files
      connection_set_params = params["connection_set"] || %{}

      # Get the name from the params
      name = connection_set_params["name"]
      old_name = socket.assigns.original_name

      # Get all existing connection sets from the socket
      all_connection_sets = socket.assigns[:connection_sets] || []

      # Check if the name already exists (excluding the current one being edited)
      name_exists =
        Enum.any?(all_connection_sets, fn set ->
          set.name == name && set.name != old_name
        end)

      if name_exists do
        # If a connection set with the same name already exists, update the socket with an error
        updated_set =
          Map.put(socket.assigns.edit_connection_set, :name_error, "This name is already in use")

        {:noreply, assign(socket, :edit_connection_set, updated_set)}
      else
        # Process uploaded files
        {ca_file, socket} =
          process_uploaded_file(socket, :ca_file, connection_set_params["ca_file"] || "")

        {client_cert_file, socket} =
          process_uploaded_file(
            socket,
            :client_cert_file,
            connection_set_params["client_cert_file"] || ""
          )

        {client_key_file, socket} =
          process_uploaded_file(
            socket,
            :client_key_file,
            connection_set_params["client_key_file"] || ""
          )

        # Update connection params with file paths
        updated_connection_set_params =
          Map.merge(connection_set_params, %{
            "ca_file" => ca_file,
            "client_cert_file" => client_cert_file,
            "client_key_file" => client_key_file
          })

        # Ensure the name field is included in the updated params
        # This is critical for the update to work correctly
        name = Map.get(socket.assigns.edit_connection_set, :name)

        updated_connection_set_params =
          if Map.has_key?(updated_connection_set_params, "name") do
            updated_connection_set_params
          else
            # If name is missing, get it from the edit_connection_set
            Map.put(updated_connection_set_params, "name", name)
          end

        # Ensure we're using the original name for the old_name parameter
        # This is critical for finding the correct connection set to update
        old_name = socket.assigns.original_name

        # Update the old_name parameter in the params
        updated_params =
          params
          |> Map.put("connection_set", updated_connection_set_params)
          |> Map.put("old_name", old_name)

        # Send the updated event to the parent LiveView
        send(self(), {:update_connection_set, updated_params})
        {:noreply, socket}
      end
    end
  end

  # Helper function to process uploaded files
  defp process_uploaded_file(socket, upload_name, existing_path) do
    CertificateHandler.process_uploaded_file(socket, upload_name, existing_path)
  end
end
