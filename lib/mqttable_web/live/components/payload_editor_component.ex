defmodule MqttableWeb.PayloadEditorComponent do
  @moduledoc """
  A simplified payload editor using pure DaisyUI components.

  Features:
  - DaisyUI textarea component
  - Format selection with radio buttons
  - Template helper (optional)
  - Minimal LiveView events to avoid input issues
  """

  use MqttableWeb, :live_component
  alias Mqttable.Templating.Engine
  require Logger

  @impl true
  def mount(socket) do
    socket =
      socket
      |> assign_new(:payload, fn -> "" end)
      |> assign_new(:payload_format, fn -> "text" end)
      |> assign_new(:show_helper, fn -> false end)

    {:ok, socket}
  end

  @impl true
  def update(assigns, socket) do
    payload = Map.get(assigns, :payload, socket.assigns[:payload] || "")
    payload_format = Map.get(assigns, :payload_format, socket.assigns[:payload_format] || "text")

    # Only update preview if payload actually changed to avoid unnecessary rendering
    previous_payload = socket.assigns[:payload]
    has_template = has_template_syntax?(payload)

    preview_content =
      if payload != previous_payload do
        render_preview_optimized(payload, has_template)
      else
        socket.assigns[:preview_content] || render_preview_optimized(payload, has_template)
      end

    socket =
      socket
      |> assign(assigns)
      |> assign(:payload, payload)
      |> assign(:payload_format, payload_format)
      |> assign(:has_template_syntax, has_template)
      |> assign(:preview_content, preview_content)
      |> assign(:preview_error, nil)

    {:ok, socket}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="form-control w-full">
      <label class="label">
        <span class="label-text font-medium">{Map.get(assigns, :label, "Payload")}</span>
      </label>
      
    <!-- Header with Horizontal Tabs and Template Helper -->
      <div class="flex items-center justify-between mb-3">
        <!-- Horizontal Format Tabs -->
        <div class="tabs tabs-lifted">
          <button
            type="button"
            class={[
              "tab tab-lifted",
              if(@payload_format == "text", do: "tab-active", else: "")
            ]}
            phx-click="format_changed"
            phx-value-format="text"
            phx-target={@myself}
          >
            Text
          </button>
          <button
            type="button"
            class={[
              "tab tab-lifted",
              if(@payload_format == "json", do: "tab-active", else: "")
            ]}
            phx-click="format_changed"
            phx-value-format="json"
            phx-target={@myself}
          >
            JSON
          </button>
          <button
            type="button"
            class={[
              "tab tab-lifted",
              if(@payload_format == "hex", do: "tab-active", else: "")
            ]}
            phx-click="format_changed"
            phx-value-format="hex"
            phx-target={@myself}
          >
            Hex
          </button>
          <button
            type="button"
            class={[
              "tab tab-lifted",
              if(@payload_format == "file", do: "tab-active", else: "")
            ]}
            phx-click="format_changed"
            phx-value-format="file"
            phx-target={@myself}
          >
            File
          </button>
        </div>
        
    <!-- Template Helper Toggle -->
        <button
          type="button"
          class={["btn btn-sm", if(@show_helper, do: "btn-primary", else: "btn-outline")]}
          phx-click="toggle_helper"
          phx-target={@myself}
        >
          <.icon name="hero-sparkles" class="size-4 mr-1" /> Template Helper
        </button>
      </div>
      
    <!-- Template Helper Panel -->
      <%= if @show_helper do %>
        <div class="bg-base-200 rounded-lg p-4 mb-3 space-y-3">
          <!-- Quick Insert Buttons -->
          <div>
            <div class="text-sm font-medium mb-2">Quick Insert:</div>
            <div class="flex flex-wrap gap-2">
              <button
                type="button"
                class="btn btn-xs btn-outline"
                phx-click="insert_template"
                phx-value-template="{{ temperature }}"
                phx-target={@myself}
              >
                🌡️ Temperature
              </button>
              <button
                type="button"
                class="btn btn-xs btn-outline"
                phx-click="insert_template"
                phx-value-template="{{ device_id }}"
                phx-target={@myself}
              >
                📱 Device ID
              </button>
              <button
                type="button"
                class="btn btn-xs btn-outline"
                phx-click="insert_template"
                phx-value-template="{{ iso8601 }}"
                phx-target={@myself}
              >
                🕐 Timestamp
              </button>
              <button
                type="button"
                class="btn btn-xs btn-outline"
                phx-click="insert_template"
                phx-value-template="{{ uuid }}"
                phx-target={@myself}
              >
                🆔 UUID
              </button>
              <button
                type="button"
                class="btn btn-xs btn-outline"
                phx-click="insert_template"
                phx-value-template="{{ 1 | random_int: 100 }}"
                phx-target={@myself}
              >
                🎲 Random Number
              </button>
              <button
                type="button"
                class="btn btn-xs btn-outline"
                phx-click="insert_template"
                phx-value-template="{{ humidity }}"
                phx-target={@myself}
              >
                💧 Humidity
              </button>
              <button
                type="button"
                class="btn btn-xs btn-outline"
                phx-click="insert_template"
                phx-value-template="{{ battery_level }}"
                phx-target={@myself}
              >
                🔋 Battery
              </button>
              <button
                type="button"
                class="btn btn-xs btn-outline"
                phx-click="insert_template"
                phx-value-template="{{ device_status }}"
                phx-target={@myself}
              >
                📊 Status
              </button>
            </div>
          </div>
          
    <!-- Example Templates -->
          <div>
            <div class="text-sm font-medium mb-2">Example Templates:</div>
            <div class="space-y-1">
              <button
                type="button"
                class="btn btn-xs btn-ghost text-left w-full justify-start"
                phx-click="insert_template"
                phx-value-template={sensor_data_example()}
                phx-target={@myself}
              >
                📊 Sensor Data
              </button>
              <button
                type="button"
                class="btn btn-xs btn-ghost text-left w-full justify-start"
                phx-click="insert_template"
                phx-value-template={device_status_example()}
                phx-target={@myself}
              >
                🔧 Device Status
              </button>
            </div>
          </div>
        </div>
      <% end %>
      
    <!-- Side-by-Side Layout: Payload Area | Live Preview Area -->
      <div class="grid grid-cols-2 gap-0 border border-base-300 rounded-lg overflow-hidden min-h-[300px]">
        <!-- Payload Area -->
        <div class="border-r border-base-300">
          <div class="bg-base-200 px-3 py-2 border-b border-base-300">
            <span class="text-sm font-medium">Payload</span>
          </div>
          <form phx-change="payload_changed" phx-target={@myself}>
            <textarea
              id={"payload-editor-#{@myself}"}
              name="payload"
              placeholder={get_placeholder(@payload_format)}
              class={[
                "textarea w-full h-[268px] border-0 rounded-none resize-none focus:outline-none",
                get_editor_class(@payload_format)
              ]}
              spellcheck="false"
            ><%= @payload %></textarea>
          </form>
        </div>
        
    <!-- Live Preview Area -->
        <div class="bg-base-50">
          <div class="bg-base-200 px-3 py-2 border-b border-base-300">
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-2">
                <.icon name="hero-eye" class="size-4 text-primary" />
                <span class="text-sm font-medium">Live Preview</span>
              </div>
              <%= if @has_template_syntax do %>
                <div class="badge badge-primary badge-sm">Template</div>
              <% end %>
            </div>
          </div>
          <div class="h-[268px] overflow-y-auto">
            {render_live_preview(assigns)}
          </div>
        </div>
      </div>
      
    <!-- Hidden inputs for form submission -->
      <input type="hidden" name="payload_format" value={@payload_format} />
    </div>
    """
  end

  # Event Handlers

  @impl true
  def handle_event("payload_changed", %{"payload" => payload}, socket) do
    # Only update preview if payload actually changed
    has_template = has_template_syntax?(payload)
    preview_content = render_preview_optimized(payload, has_template)

    socket =
      socket
      |> assign(:payload, payload)
      |> assign(:has_template_syntax, has_template)
      |> assign(:preview_content, preview_content)

    # Notify parent component
    send(self(), {:payload_editor_changed, payload, socket.assigns.payload_format})

    {:noreply, socket}
  end

  @impl true
  def handle_event("format_changed", %{"format" => format}, socket) do
    socket = assign(socket, :payload_format, format)

    # Notify parent component
    send(self(), {:payload_editor_changed, socket.assigns.payload, format})

    {:noreply, socket}
  end

  @impl true
  def handle_event("toggle_helper", _params, socket) do
    {:noreply, assign(socket, :show_helper, !socket.assigns.show_helper)}
  end

  @impl true
  def handle_event("insert_template", %{"template" => template}, socket) do
    # Push event to JavaScript to insert at cursor position
    {:noreply,
     push_event(socket, "insert_at_cursor", %{
       target_id: "payload-editor-#{socket.assigns.myself}",
       text: template
     })}
  end

  # Helper Functions

  defp has_template_syntax?(content) when is_binary(content) do
    String.contains?(content, "{{") || String.contains?(content, "{%")
  end

  defp has_template_syntax?(_), do: false

  defp render_preview_optimized(content, has_template) do
    if has_template do
      # Use a lightweight preview context instead of full function generation
      preview_context = get_preview_context()

      case Engine.render(content, preview_context, %{}) do
        {:ok, rendered} ->
          # Truncate if too long for preview
          if String.length(rendered) > 300 do
            String.slice(rendered, 0, 300) <> "..."
          else
            rendered
          end

        {:error, reason} ->
          "❌ Template Error: #{reason}"
      end
    else
      content
    end
  end

  # Lightweight preview context with pre-computed values to avoid expensive function calls
  defp get_preview_context do
    %{
      "temperature" => 22.5,
      "humidity" => 65.0,
      "device_id" => "device_abc123",
      "iso8601" => "2024-01-01T12:00:00Z",
      "uuid" => "550e8400-e29b-41d4-a716-************",
      "battery_level" => 85,
      "device_status" => "online",
      "timestamp" => 1_704_110_400,
      "random_int" => 42,
      "random_float" => 3.14
    }
  end

  defp get_placeholder(format) do
    case format do
      "json" -> ~s({"message": "Hello World", "value": ) <> "{{ 1 | random_int: 100 }}" <> "}"
      "hex" -> "48656C6C6F20576F726C64"
      _ -> "Hello " <> "{{ device_id }}" <> ", temperature is " <> "{{ temperature }}" <> "°C"
    end
  end

  defp get_editor_class(format) do
    case format do
      "json" -> "font-mono text-sm"
      "hex" -> "font-mono text-sm uppercase tracking-wider"
      _ -> ""
    end
  end

  defp sensor_data_example do
    """
    {
      "device_id": "{{ device_id }}",
      "timestamp": "{{ iso8601 }}",
      "temperature": {{ 20 | temperature: 30 }},
      "humidity": {{ humidity }},
      "battery": {{ battery_level }}
    }
    """
  end

  defp device_status_example do
    """
    Device {{ device_id }} is {{ device_status }}.
    Uptime: {{ uptime }} seconds
    Firmware: {{ firmware_version }}
    Last seen: {{ iso8601 }}
    """
  end

  # Render live preview based on format and content
  defp render_live_preview(assigns) do
    ~H"""
    <%= case @payload_format do %>
      <% "json" -> %>
        <%= if String.trim(@payload) != "" do %>
          <%= if @has_template_syntax do %>
            <!-- Template preview for JSON -->
            <div class="p-3">
              <div class="bg-base-100 rounded-lg">
                <.render_json_viewer payload={@preview_content} id={"preview-json-#{@myself}"} />
              </div>
            </div>
          <% else %>
            <!-- Direct JSON viewer -->
            <div class="p-3">
              <div class="bg-base-100 rounded-lg">
                <.render_json_viewer payload={@payload} id={"payload-json-#{@myself}"} />
              </div>
            </div>
          <% end %>
        <% else %>
          <div class="p-3 text-center text-gray-500">
            <.icon name="hero-code-bracket" class="size-8 mx-auto mb-2 opacity-50" />
            <p class="text-sm">Enter JSON to see preview</p>
          </div>
        <% end %>
      <% "hex" -> %>
        <%= if String.trim(@payload) != "" do %>
          <div class="p-3">
            <div class="bg-white border border-base-300 rounded p-3 font-mono text-sm">
              <div class="text-xs text-gray-500 mb-2">Hex Preview:</div>
              <div class="break-all">
                <%= if @has_template_syntax do %>
                  {@preview_content}
                <% else %>
                  {@payload}
                <% end %>
              </div>
            </div>
          </div>
        <% else %>
          <div class="p-3 text-center text-gray-500">
            <.icon name="hero-hashtag" class="size-8 mx-auto mb-2 opacity-50" />
            <p class="text-sm">Enter hex data to see preview</p>
          </div>
        <% end %>
      <% "file" -> %>
        <div class="p-3 text-center text-gray-500">
          <.icon name="hero-document" class="size-8 mx-auto mb-2 opacity-50" />
          <p class="text-sm">File upload preview</p>
          <p class="text-xs text-gray-400">Upload a file to see preview</p>
        </div>
      <% _ -> %>
        <!-- Text format -->
        <%= if String.trim(@payload) != "" do %>
          <div class="p-3">
            <div class="bg-white border border-base-300 rounded p-3">
              <div class="text-xs text-gray-500 mb-2">Text Preview:</div>
              <pre class="text-sm whitespace-pre-wrap text-gray-800"><%= if @has_template_syntax, do: @preview_content, else: @payload %></pre>
            </div>
          </div>
        <% else %>
          <div class="p-3 text-center text-gray-500">
            <.icon name="hero-document-text" class="size-8 mx-auto mb-2 opacity-50" />
            <p class="text-sm">Enter text to see preview</p>
          </div>
        <% end %>
    <% end %>
    """
  end

  # Render JSON viewer component (copied from MessageDetailModalComponent)
  defp render_json_viewer(assigns) do
    ~H"""
    <%= if is_valid_json?(@payload) do %>
      <div
        id={"json-viewer-#{@id}"}
        class="json-viewer-container"
        phx-hook="JsonViewer"
        data-json={@payload}
      >
        <!-- JSON viewer will be rendered here by the hook -->
      </div>
    <% else %>
      <div class="json-viewer-error">
        <div class="json-error-header">Invalid JSON Data</div>
        <pre class="json-raw-content"><%= @payload %></pre>
      </div>
    <% end %>
    """
  end

  # Check if payload is valid JSON (copied from MessageDetailModalComponent)
  defp is_valid_json?(payload) when is_binary(payload) do
    case Jason.decode(payload) do
      {:ok, _} -> true
      {:error, _} -> false
    end
  end

  defp is_valid_json?(_), do: false
end
