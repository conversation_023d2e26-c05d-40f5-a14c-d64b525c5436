defmodule MqttableWeb.VariableTableComponent do
  @moduledoc """
  LiveComponent for rendering a variable table.
  This component optimizes rendering by implementing the update/2 callback
  to only re-render when necessary.
  """
  use MqttableWeb, :live_component

  @impl true
  def render(assigns) do
    # Ensure active_set is assigned, even if it's nil
    assigns = assign_new(assigns, :active_set, fn -> nil end)

    ~H"""
    <div class="bg-base-100 p-4 rounded-lg border border-gray-200">
      <div class="flex justify-between items-center mb-4">
        <h3 class="font-medium">Variables</h3>
      </div>

      <table class="table table-zebra w-full table-with-dividers">
        <thead>
          <tr>
            <th>Name</th>
            <th>Value</th>
            <th class="w-16"></th>
          </tr>
        </thead>
        <tbody>
          <% variables = Map.get(@active_set, :variables, [])
          duplicate_colors = get_duplicate_colors(variables) %>
          <%= for var <- variables do %>
            <% # Get color class for duplicate names
            color_class =
              if var.name != "" && Map.has_key?(duplicate_colors, var.name) do
                Map.get(duplicate_colors, var.name)
              else
                ""
              end %>
            <tr class="table-row-hover">
              <td class="px-2">
                <span class={"variable-text #{color_class}"}>{var.name}</span>
              </td>
              <td class="px-2"><span class="variable-text">{var.value}</span></td>
              <td class="px-2">
                <div class="flex space-x-2">
                  <button
                    phx-click="delete_variable"
                    phx-value-name={var.name}
                    class="btn btn-xs btn-ghost text-gray-500"
                    data-confirm="Are you sure you want to delete this variable?"
                  >
                    <.icon name="hero-minus-circle" class="size-4" />
                  </button>
                </div>
              </td>
            </tr>
          <% end %>
        </tbody>
      </table>
    </div>
    """
  end

  @impl true
  def update(assigns, socket) do
    # Get current values from socket
    current_active_set = socket.assigns[:active_set]

    # Check if the active connection set has changed
    set_changed = current_active_set != assigns[:active_set]

    # Always ensure active_set is assigned to the socket
    socket = assign(socket, :active_set, assigns[:active_set])

    # Only update the rest of the assigns if the active connection set has changed
    if set_changed do
      {:ok, assign(socket, assigns)}
    else
      {:ok, socket}
    end
  end

  # Helper function to get duplicate colors
  defp get_duplicate_colors(variables) do
    # Count occurrences of each name
    name_counts =
      variables
      |> Enum.filter(fn var -> var.name != "" end)
      |> Enum.group_by(fn var -> var.name end)
      |> Enum.filter(fn {_name, vars} -> length(vars) > 1 end)
      |> Map.new(fn {name, _vars} -> {name, true} end)

    # Assign colors to duplicates
    name_counts
    |> Map.keys()
    |> Enum.with_index()
    |> Enum.map(fn {name, index} ->
      color =
        case rem(index, 5) do
          0 -> "text-red-500"
          1 -> "text-blue-500"
          2 -> "text-green-500"
          3 -> "text-yellow-500"
          4 -> "text-purple-500"
        end

      {name, color}
    end)
    |> Map.new()
  end
end
