defmodule MqttableWeb.BrokerTabsComponent do
  @moduledoc """
  LiveComponent for rendering broker tabs in a VSCode-like interface.
  This component displays brokers as tabs with drag-and-drop reordering capability.
  """
  use MqttableWeb, :live_component

  import MqttableWeb.CoreComponents

  @impl true
  def render(assigns) do
    # Check if broker tabs are collapsed by looking at active broker's expanded state
    broker_tabs_collapsed = is_broker_tabs_collapsed(assigns)
    assigns = assign(assigns, :broker_tabs_collapsed, broker_tabs_collapsed)

    ~H"""
    <div class={[
      "broker-tabs-container bg-base-100 transition-all duration-300",
      if(@broker_tabs_collapsed, do: "py-1 px-2", else: "py-1 px-2")
    ]}>
      <div class="flex items-center">
        <!-- Collapse/Expand Button -->
        <button
          type="button"
          class="btn btn-ghost btn-sm mr-2 flex-shrink-0"
          phx-click="toggle_broker_tabs_collapsed"
          phx-target={@myself}
          title={if @broker_tabs_collapsed, do: "Expand broker tabs", else: "Collapse broker tabs"}
        >
          <.icon
            name={if @broker_tabs_collapsed, do: "hero-chevron-down", else: "hero-chevron-up"}
            class="w-4 h-4"
          />
        </button>
        
    <!-- Broker Tabs -->
        <div
          :if={!@broker_tabs_collapsed}
          id="broker-tabs-sortable"
          class="flex items-center flex-1 overflow-x-auto"
          phx-hook="BrokerTabsSortable"
        >
          <%= for broker <- @connection_sets do %>
            <div
              class={[
                "broker-tab group flex items-center px-2 py-1 mr-1 rounded-lg cursor-pointer transition-all duration-200 min-w-0 max-w-60",
                "hover:bg-base-300",
                if(@active_connection_set && @active_connection_set.name == broker.name) do
                  "bg-base-100 border-2 border-yellow-500 text-base-content font-medium shadow-sm"
                else
                  "bg-base-200 border-2 border-transparent text-base-content/80 hover:text-base-content"
                end
              ]}
              phx-click="select_broker_tab"
              phx-value-name={broker.name}
              data-broker-name={broker.name}
            >
              <!-- Broker Name -->
              <span class="truncate text-sm">
                {broker.name}
              </span>
              
    <!-- Close Button (only show on hover for inactive tabs, always show for active) -->
              <span
                class={[
                  "ml-2 p-1 rounded hover:bg-error/20 text-error/60 hover:text-error transition-colors flex-shrink-0 cursor-pointer",
                  if(@active_connection_set && @active_connection_set.name == broker.name) do
                    "opacity-100"
                  else
                    "opacity-0 group-hover:opacity-100"
                  end
                ]}
                phx-click="show_delete_confirm"
                phx-value-name={broker.name}
                phx-target={@myself}
              >
                <.icon name="hero-x-mark" class="w-3 h-3" />
              </span>
            </div>
          <% end %>
        </div>
        
    <!-- Collapsed State: Show active broker name only -->
        <div :if={@broker_tabs_collapsed} class="flex items-center flex-1">
          <span class="text-sm text-base-content/80">
            <%= if @active_connection_set do %>
              Active: <span class="font-medium">{@active_connection_set.name}</span>
            <% else %>
              No active broker
            <% end %>
          </span>
        </div>
        
    <!-- Add New Broker Button -->
        <button
          type="button"
          class="btn btn-primary btn-sm ml-2 flex-shrink-0"
          phx-click="open_connection_set_modal"
          phx-value-type="new_connection_set"
          title="Add new broker"
        >
          +
        </button>
      </div>
      
    <!-- Delete Confirmation Modal (DaisyUI) -->
      <dialog
        id="delete-broker-modal"
        class={["modal", if(assigns[:show_delete_confirm], do: "modal-open", else: "")]}
      >
        <div class="modal-box">
          <h3 class="text-lg font-bold text-error">
            <.icon name="hero-exclamation-triangle" class="w-5 h-5 inline mr-2" /> Delete Broker
          </h3>
          <p class="py-4">
            Are you sure you want to delete the broker '<span class="font-semibold">{assigns[:broker_to_delete] || ""}</span>'?
            <br /><br />
            This will disconnect all MQTT connections and remove all trace data. This action cannot be undone.
          </p>
          <div class="modal-action">
            <button
              type="button"
              class="btn btn-ghost"
              phx-click="cancel_delete_broker"
              phx-target={@myself}
            >
              Cancel
            </button>
            <button
              type="button"
              class="btn btn-error"
              phx-click="confirm_delete_broker"
              phx-target={@myself}
            >
              Delete
            </button>
          </div>
        </div>
        <form method="dialog" class="modal-backdrop">
          <button type="button" phx-click="cancel_delete_broker" phx-target={@myself}>close</button>
        </form>
      </dialog>
    </div>
    """
  end

  @impl true
  def update(assigns, socket) do
    socket =
      socket
      |> assign(assigns)
      |> assign_new(:show_delete_confirm, fn -> false end)
      |> assign_new(:broker_to_delete, fn -> nil end)

    {:ok, socket}
  end

  @impl true
  def handle_event("toggle_broker_tabs_collapsed", _params, socket) do
    active_broker_name = get_active_broker_name(socket.assigns)

    if active_broker_name do
      # Get current expanded state
      ui_state = Mqttable.ConnectionSets.get_ui_state()
      expanded_sets = Map.get(ui_state, :expanded_sets, %{})
      current_state = Map.get(expanded_sets, active_broker_name, "expanded")

      # Toggle between "collapsed" and "expanded"
      new_state = if current_state == "collapsed", do: "expanded", else: "collapsed"

      # Update expanded_sets
      updated_expanded_sets = Map.put(expanded_sets, active_broker_name, new_state)
      updated_ui_state = Map.put(ui_state, :expanded_sets, updated_expanded_sets)
      Mqttable.ConnectionSets.update_ui_state(updated_ui_state)

      # Send message to parent LiveView to update trace table height
      send(self(), {:broker_tabs_collapsed_changed, new_state == "collapsed"})
    end

    {:noreply, socket}
  end

  @impl true
  def handle_event("show_delete_confirm", %{"name" => broker_name}, socket) do
    socket =
      socket
      |> assign(:show_delete_confirm, true)
      |> assign(:broker_to_delete, broker_name)

    {:noreply, socket}
  end

  @impl true
  def handle_event("confirm_delete_broker", _params, socket) do
    broker_name = socket.assigns.broker_to_delete

    if broker_name do
      # Send the close broker tab event to the parent LiveView
      send(self(), {:close_broker_tab, broker_name})
    end

    # Hide the confirmation dialog
    socket =
      socket
      |> assign(:show_delete_confirm, false)
      |> assign(:broker_to_delete, nil)

    {:noreply, socket}
  end

  @impl true
  def handle_event("cancel_delete_broker", _params, socket) do
    # Hide the confirmation dialog without deleting
    socket =
      socket
      |> assign(:show_delete_confirm, false)
      |> assign(:broker_to_delete, nil)

    {:noreply, socket}
  end

  # Helper function to check if broker tabs are collapsed
  defp is_broker_tabs_collapsed(assigns) do
    active_broker_name = get_active_broker_name(assigns)

    if active_broker_name do
      ui_state = Mqttable.ConnectionSets.get_ui_state()
      expanded_sets = Map.get(ui_state, :expanded_sets, %{})
      Map.get(expanded_sets, active_broker_name, "expanded") == "collapsed"
    else
      false
    end
  end

  # Helper function to get active broker name from assigns
  defp get_active_broker_name(assigns) do
    if assigns[:active_connection_set] do
      assigns.active_connection_set.name
    else
      nil
    end
  end
end
