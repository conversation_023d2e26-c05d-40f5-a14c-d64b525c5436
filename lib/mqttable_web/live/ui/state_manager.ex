defmodule MqttableWeb.UI.StateManager do
  @moduledoc """
  Module for managing UI state.
  This module provides functions for managing UI state, such as expanded sets.
  """

  import Phoenix.Component
  import Phoenix.LiveView

  # Import verified routes
  use Phoenix.VerifiedRoutes,
    endpoint: MqttableWeb.Endpoint,
    router: MqttableWeb.Router,
    statics: MqttableWeb.static_paths()

  alias Mqttable.ConnectionSets
  alias MqttableWeb.Utils.ConnectionHelpers

  @doc """
  Handles the close_modal event.
  """
  def handle_close_modal(socket) do
    {:noreply,
     socket
     |> assign(:show_modal, false)
     |> assign(:modal_type, nil)
     |> assign(:edit_var, nil)
     |> assign(:edit_connection_set, nil)
     |> assign(:connection_set, nil)}
  end

  @doc """
  Handles the open_connections_modal event.
  """
  def handle_open_connections_modal(socket) do
    # Check if there are any connection sets
    if Enum.empty?(socket.assigns.connection_sets) do
      # If no connection sets exist, open the new connection set modal
      variables = [%{name: "", value: "", enabled: true}]

      new_set = %{
        protocol: "mqtt",
        host: "",
        port: "1883",
        color: "blue",
        variables: variables
      }

      socket =
        socket
        |> assign(:show_modal, true)
        |> assign(:modal_type, :new_connection_set)
        |> assign(:edit_connection_set, new_set)

      # Schedule focus on the host field after the modal is rendered
      # Process.send_after(self(), {:focus_element, "connection-set-host"}, 100)

      {:noreply, socket}
    else
      # If connection sets exist, show the first one
      first_set = List.first(socket.assigns.connection_sets)

      # Load initial trace messages for the first broker (first page only)
      trace_messages =
        case Mqttable.TraceManager.get_messages_paginated(first_set.name, 50, 0) do
          {:ok, messages, _has_more} -> messages
          _error -> []
        end

      # Mark this change as initiated locally to prevent loops
      socket = assign(socket, :active_set_change_source, "local")

      # Update the active connection set in the UI state to broadcast to all clients
      ConnectionSets.update_active_connection_set(first_set.name)

      {:noreply,
       socket
       |> assign(:active_connection_set, first_set)
       |> stream(:trace_messages, trace_messages)}
    end
  end

  @doc """
  Handles the connection_sets_updated message.
  """
  def handle_connection_sets_updated(socket, updated_connection_sets) do
    # Get the current active connection set name
    active_connection_set_name =
      if socket.assigns.active_connection_set,
        do: socket.assigns.active_connection_set.name,
        else: nil

    # Use our helper function to select appropriate active broker
    updated_active_connection_set =
      ConnectionHelpers.select_active_broker(updated_connection_sets, active_connection_set_name)

    # Since we no longer use broker menu, we don't need to manage broker expansion states
    # Only keep connection table expansion states
    expanded_sets = socket.assigns.expanded_sets

    # Save the updated expanded sets state
    ConnectionSets.update_expanded_sets(expanded_sets)

    # Only reload trace messages if the active broker has actually changed
    socket =
      if updated_active_connection_set &&
           (socket.assigns.active_connection_set == nil ||
              socket.assigns.active_connection_set.name != updated_active_connection_set.name) do
        # Active broker changed, reload trace messages
        trace_messages = Mqttable.TraceManager.get_messages(updated_active_connection_set.name)

        socket
        |> assign(:connection_sets, updated_connection_sets)
        |> assign(:expanded_sets, expanded_sets)
        |> assign(:active_connection_set, updated_active_connection_set)
        |> stream(:trace_messages, trace_messages)
      else
        # Active broker didn't change, just update other assigns
        socket
        |> assign(:connection_sets, updated_connection_sets)
        |> assign(:expanded_sets, expanded_sets)
        |> assign(:active_connection_set, updated_active_connection_set)
      end

    {:noreply, socket}
  end

  @doc """
  Handles the ui_state_updated message.
  """
  def handle_ui_state_updated(socket, updated_ui_state) do
    # Get the expanded sets from the updated UI state
    expanded_sets = Map.get(updated_ui_state, :expanded_sets, %{})

    # Get the active connection set name from the updated UI state
    active_set_name = Map.get(updated_ui_state, :active_connection_set)

    # Since we no longer use broker menu, we don't need to manage broker expansion states
    # Only keep connection table expansion states
    updated_expanded_sets = expanded_sets

    socket = assign(socket, :expanded_sets, updated_expanded_sets)

    # Check if this change was initiated locally
    is_local_change = socket.assigns[:active_set_change_source] == "local"

    # If there's an active connection set name and it's different from the current one
    if active_set_name &&
         (socket.assigns.active_connection_set == nil ||
            socket.assigns.active_connection_set.name != active_set_name) do
      # Find the connection set by name
      set =
        ConnectionHelpers.find_connection_set_by_name(
          socket.assigns.connection_sets,
          active_set_name
        )

      if set do
        # Load all trace messages for the new active broker
        trace_messages = Mqttable.TraceManager.get_messages(active_set_name)

        # Update the active connection set and URL, but only if this change wasn't initiated by this client
        if is_local_change do
          # Clear the source flag and just update the active connection set without pushing a new URL
          {:noreply,
           socket
           |> assign(:active_connection_set, set)
           |> assign(:active_set_change_source, nil)
           |> stream(:trace_messages, trace_messages)}
        else
          # This change came from another client, just update the active set without changing URL
          # This avoids triggering handle_params and prevents double processing
          {:noreply,
           socket
           |> assign(:active_connection_set, set)
           |> stream(:trace_messages, trace_messages)}
        end
      else
        # Clear the source flag if it was set
        socket =
          if is_local_change do
            assign(socket, :active_set_change_source, nil)
          else
            socket
          end

        {:noreply, socket}
      end
    else
      # Clear the source flag if it was set
      socket =
        if is_local_change do
          assign(socket, :active_set_change_source, nil)
        else
          socket
        end

      # Even if the active connection set didn't change, we still need to return the socket
      # with updated expanded_sets to ensure the UI reflects the changes
      {:noreply, socket}
    end
  end

  @doc """
  Handles the focus_element message.
  """
  def handle_focus_element(socket, element_id) do
    {:noreply, push_event(socket, "focus_element", %{id: element_id})}
  end
end
