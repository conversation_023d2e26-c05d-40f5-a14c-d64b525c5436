defmodule MqttableWeb.ConnectionsLive.ModalStateManager do
  @moduledoc """
  Manages modal state operations for the connections LiveView.

  This module handles:
  - Subscription modal state management
  - Settings modal state management
  - Scheduled message modal state management
  - Modal opening and closing operations
  - Modal state initialization and cleanup

  All functions follow functional programming principles with pattern matching,
  pure functions where possible, and proper error handling with tagged tuples.
  """

  import Phoenix.LiveView, only: [put_flash: 3]
  import Phoenix.Component, only: [assign: 3]
  alias MqttableWeb.Utils.ConnectionHelpers

  # Type definitions
  @type socket :: Phoenix.LiveView.Socket.t()
  @type connection_sets :: [map()]

  @doc """
  Handles opening the subscription modal for a connection set.
  """
  @spec handle_open_subscription_modal(socket(), String.t()) :: {:noreply, socket()}
  def handle_open_subscription_modal(socket, set_name) do
    active_set =
      ConnectionHelpers.find_connection_set_by_name(socket.assigns.connection_sets, set_name)

    if active_set do
      socket =
        socket
        |> assign(:active_connection_set, active_set)
        |> assign(:show_subscription_modal, true)
        |> assign(:pre_selected_client_id, nil)

      {:noreply, socket}
    else
      {:noreply, socket}
    end
  end

  @doc """
  Handles opening the subscription modal for a specific client.
  """
  @spec handle_open_subscription_modal_for_client(socket(), String.t(), String.t()) ::
          {:noreply, socket()}
  def handle_open_subscription_modal_for_client(socket, set_name, client_id) do
    active_set =
      ConnectionHelpers.find_connection_set_by_name(socket.assigns.connection_sets, set_name)

    if active_set do
      socket =
        socket
        |> assign(:active_connection_set, active_set)
        |> assign(:show_subscription_modal, true)
        |> assign(:pre_selected_client_id, client_id)

      {:noreply, socket}
    else
      {:noreply, socket}
    end
  end

  @doc """
  Handles closing the subscription modal and resetting its state.
  """
  @spec handle_close_subscription_modal(socket()) :: {:noreply, socket()}
  def handle_close_subscription_modal(socket) do
    socket =
      socket
      |> assign(:show_subscription_modal, false)
      |> assign(:edit_mode, false)
      |> assign(:client_id, nil)
      |> assign(:topic, nil)
      |> assign(:qos, 0)
      |> assign(:nl, false)
      |> assign(:rap, false)
      |> assign(:rh, 0)
      |> assign(:sub_id, nil)
      |> assign(:index, nil)

    {:noreply, socket}
  end

  @doc """
  Handles opening the settings modal.
  """
  @spec handle_open_settings_modal(socket()) :: {:noreply, socket()}
  def handle_open_settings_modal(socket) do
    {:noreply, assign(socket, :show_settings_modal, true)}
  end

  @doc """
  Handles closing the settings modal.
  """
  @spec handle_close_settings_modal(socket()) :: {:noreply, socket()}
  def handle_close_settings_modal(socket) do
    {:noreply, assign(socket, :show_settings_modal, false)}
  end

  @doc """
  Handles opening the scheduled message modal for a specific client.
  """
  @spec handle_open_scheduled_message_modal_for_client(socket(), String.t(), String.t()) ::
          {:noreply, socket()}
  def handle_open_scheduled_message_modal_for_client(socket, set_name, client_id) do
    active_set =
      ConnectionHelpers.find_connection_set_by_name(socket.assigns.connection_sets, set_name)

    if active_set do
      socket =
        socket
        |> assign(:active_connection_set, active_set)
        |> assign(:show_scheduled_message_modal, true)
        |> assign(:pre_selected_client_id, client_id)
        |> assign(:scheduled_message_edit_mode, false)
        |> assign(:scheduled_message_edit_index, nil)
        |> assign(:scheduled_message_data, nil)

      {:noreply, socket}
    else
      {:noreply, socket}
    end
  end

  @doc """
  Handles closing the scheduled message modal and resetting its state.
  """
  @spec handle_close_scheduled_message_modal(socket()) :: {:noreply, socket()}
  def handle_close_scheduled_message_modal(socket) do
    socket =
      socket
      |> assign(:show_scheduled_message_modal, false)
      |> assign(:pre_selected_client_id, nil)
      |> assign(:scheduled_message_edit_mode, false)
      |> assign(:scheduled_message_edit_index, nil)
      |> assign(:scheduled_message_data, nil)

    {:noreply, socket}
  end

  @doc """
  Handles editing a scheduled message by opening the modal with pre-filled data.
  """
  @spec handle_edit_scheduled_message(socket(), String.t(), String.t()) :: {:noreply, socket()}
  def handle_edit_scheduled_message(socket, client_id, index_str) do
    index = String.to_integer(index_str)

    case find_connection_and_scheduled_message(socket.assigns.connection_sets, client_id, index) do
      {connection_set, _connection, scheduled_message} ->
        socket =
          socket
          |> assign(:active_connection_set, connection_set)
          |> assign(:show_scheduled_message_modal, true)
          |> assign(:pre_selected_client_id, client_id)
          |> assign(:scheduled_message_edit_mode, true)
          |> assign(:scheduled_message_edit_index, index)
          |> assign(:scheduled_message_data, scheduled_message)

        {:noreply, socket}

      nil ->
        {:noreply, put_flash(socket, :error, "Scheduled message not found")}
    end
  end

  @doc """
  Initializes modal-related assigns for the socket.
  """
  @spec initialize_modal_assigns(socket()) :: socket()
  def initialize_modal_assigns(socket) do
    socket
    |> assign(:show_subscription_modal, false)
    |> assign(:pre_selected_client_id, nil)
    |> assign(:edit_mode, false)
    |> assign(:client_id, nil)
    |> assign(:topic, nil)
    |> assign(:qos, 0)
    |> assign(:nl, false)
    |> assign(:rap, false)
    |> assign(:rh, 0)
    |> assign(:sub_id, nil)
    |> assign(:index, nil)
    |> assign(:show_modal, false)
    |> assign(:modal_type, nil)
    |> assign(:show_send_modal, false)
    |> assign(:show_detail_modal, false)
    |> assign(:detail_modal_message, nil)
    |> assign(:payload_view_type, "plaintext")
    |> assign(:show_settings_modal, false)
    |> assign(:show_scheduled_message_modal, false)
    |> assign(:scheduled_message_edit_mode, false)
    |> assign(:scheduled_message_edit_index, nil)
    |> assign(:scheduled_message_data, nil)
    |> assign(:show_template_manager, false)
  end

  @doc """
  Handles factory reset completion by resetting all modal states.
  """
  @spec handle_factory_reset_completed(socket()) :: {:noreply, socket()}
  def handle_factory_reset_completed(socket) do
    socket =
      socket
      |> assign(:show_settings_modal, false)
      |> assign(:connection_sets, [])
      |> assign(:active_connection_set, nil)
      |> Phoenix.LiveView.stream(:trace_messages, [])
      |> assign(:send_modal_form_state, %{})
      |> assign_page_title(nil)
      |> put_flash(:info, "Factory reset completed successfully")

    {:noreply, socket}
  end

  # Private functions

  @spec find_connection_and_scheduled_message(connection_sets(), String.t(), integer()) ::
          {map(), map(), map()} | nil
  defp find_connection_and_scheduled_message(connection_sets, client_id, index) do
    Enum.find_value(connection_sets, fn connection_set ->
      case Enum.find(connection_set.connections, fn conn -> conn.client_id == client_id end) do
        nil ->
          nil

        connection ->
          scheduled_messages = Map.get(connection, :scheduled_messages, [])

          if index >= 0 && index < length(scheduled_messages) do
            scheduled_message = Enum.at(scheduled_messages, index)
            {connection_set, connection, scheduled_message}
          else
            nil
          end
      end
    end)
  end

  @spec assign_page_title(socket(), map() | nil) :: socket()
  defp assign_page_title(socket, active_connection_set) do
    page_title =
      if active_connection_set do
        "#{active_connection_set.name} - Mqttable"
      else
        "Mqttable"
      end

    assign(socket, :page_title, page_title)
  end
end
