defmodule MqttableWeb.IconMenu do
  @moduledoc """
  Provides an icon-only menu component based on DaisyUI.
  """
  use Phoenix.Component
  use Gettext, backend: MqttableWeb.Gettext

  import MqttableWeb.CoreComponents

  @doc """
  Renders a vertical icon-only menu with items positioned at the top and bottom.

  ## Examples

      <.icon_menu>
        <:top_item icon="hero-inbox-stack" navigate={~p"/"} tooltip="Connections" />
        <:top_item icon="hero-wrench-screwdriver" navigate={~p"/toolbox"} tooltip="Toolbox" />

        <:bottom_item icon="hero-cog-6-tooth" navigate={~p"/settings"} tooltip="Settings" />
        <:bottom_item icon="hero-user" navigate={~p"/account"} tooltip="Account" />
      </.icon_menu>
  """
  attr :class, :string, default: nil

  slot :top_item, required: true do
    attr :icon, :string, required: true
    attr :navigate, :string
    attr :patch, :string
    attr :href, :string
    attr :tooltip, :string
    attr :active, :boolean
    attr :phx_click, :string
  end

  slot :bottom_item do
    attr :icon, :string, required: true
    attr :navigate, :string
    attr :patch, :string
    attr :href, :string
    attr :tooltip, :string
    attr :active, :boolean
    attr :phx_click, :string
  end

  def icon_menu(assigns) do
    # Check if this is the panel version
    is_panel_menu = String.contains?(assigns[:class] || "", "panel-icon-menu")

    base_classes =
      if is_panel_menu do
        "menu menu-vertical bg-base-200 w-16 h-full flex flex-col justify-between border-r border-base-300"
      else
        "menu menu-vertical bg-base-200 w-12 h-screen flex flex-col justify-between shadow-md z-40"
      end

    assigns = assign(assigns, :base_classes, base_classes)

    ~H"""
    <div class={[
      @base_classes,
      @class
    ]}>
      <ul class="menu-top pt-2 flex flex-col items-center w-full">
        <%= for item <- @top_item do %>
          <li class="mb-2 w-full flex justify-center">
            <.menu_item {item} />
          </li>
        <% end %>
      </ul>
      <ul class="menu-bottom pb-2 flex flex-col items-center w-full">
        <%= for item <- @bottom_item do %>
          <li class="mb-2 w-full flex justify-center last:mb-0">
            <.menu_item {item} />
          </li>
        <% end %>
      </ul>
    </div>
    """
  end

  defp menu_item(assigns) do
    attrs =
      assigns
      |> Map.take([:navigate, :patch, :href])
      |> Enum.find(fn {_, v} -> not is_nil(v) end)
      |> case do
        {k, v} -> [{k, v}]
        nil -> []
      end

    # Check if active is present in assigns, default to false if not
    is_active = Map.get(assigns, :active, false)

    # Check if phx-click is present
    phx_click = Map.get(assigns, :phx_click)
    has_phx_click = not is_nil(phx_click)
    phx_attrs = if has_phx_click, do: [{"phx-click", phx_click}], else: []

    # Determine if we should use a button (for phx-click only) or a link
    use_button = has_phx_click and attrs == []

    assigns =
      assigns
      |> assign(:attrs, attrs)
      |> assign(:is_active, is_active)
      |> assign(:phx_attrs, phx_attrs)
      |> assign(:use_button, use_button)

    ~H"""
    <div class="relative flex justify-center w-full">
      <div class={[
        "absolute left-0 top-0 h-full w-1 bg-yellow-500 transition-all duration-300",
        @is_active && "opacity-100",
        !@is_active && "opacity-0"
      ]}>
      </div>
      <%= if @use_button do %>
        <button
          type="button"
          class={[
            "tooltip tooltip-right flex justify-center items-center w-8 h-8 rounded-lg transition-all duration-300",
            @is_active && "bg-transparent"
          ]}
          data-tip={@tooltip}
          {@phx_attrs}
        >
          <.icon name={@icon} class="size-3.5" />
        </button>
      <% else %>
        <.link
          class={[
            "tooltip tooltip-right flex justify-center items-center w-8 h-8 rounded-lg transition-all duration-300",
            @is_active && "bg-transparent"
          ]}
          data-tip={@tooltip}
          {@attrs}
          {@phx_attrs}
        >
          <.icon name={@icon} class="size-3.5" />
        </.link>
      <% end %>
    </div>
    """
  end
end
