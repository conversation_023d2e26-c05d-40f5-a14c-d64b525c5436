defmodule Mqttable.Settings.Server do
  @moduledoc """
  GenServer for managing application settings state.
  Provides in-memory storage with immediate persistence to disk.
  Broadcasts changes to all connected clients.
  """
  use GenServer
  require Logger

  alias Mqttable.Settings.Storage
  alias Phoenix.PubSub

  @pubsub_topic "settings"

  # Client API

  @doc """
  Starts the settings server.
  """
  def start_link(opts \\ []) do
    GenServer.start_link(__MODULE__, opts, name: __MODULE__)
  end

  @doc """
  Gets all settings.
  """
  def get_all do
    GenServer.call(__MODULE__, :get_all)
  end

  @doc """
  Gets a specific setting value.
  """
  def get(key) when is_atom(key) do
    GenServer.call(__MODULE__, {:get, key})
  end

  @doc """
  Updates settings.
  """
  def update(settings) when is_map(settings) do
    GenServer.cast(__MODULE__, {:update, settings})
  end

  @doc """
  Updates a specific setting.
  """
  def put(key, value) when is_atom(key) do
    GenServer.cast(__MODULE__, {:put, key, value})
  end

  # Server Callbacks

  @impl true
  def init(_opts) do
    # Load settings from disk
    settings =
      case Storage.load() do
        {:ok, loaded_settings} ->
          loaded_settings

        {:error, reason} ->
          Logger.error("Failed to load settings: #{reason}")
          Storage.default_settings()
      end

    {:ok, %{settings: settings}}
  end

  @impl true
  def handle_call(:get_all, _from, state) do
    {:reply, state.settings, state}
  end

  @impl true
  def handle_call({:get, key}, _from, state) do
    value = Map.get(state.settings, key)
    {:reply, value, state}
  end

  @impl true
  def handle_cast({:update, new_settings}, state) do
    # Merge new settings with existing ones
    updated_settings = Map.merge(state.settings, new_settings)

    # Save immediately to disk
    case Storage.save(updated_settings) do
      {:ok, _path} ->
        Logger.info("Settings saved to disk")

        # Broadcast the update to all clients
        PubSub.broadcast(
          Mqttable.PubSub,
          @pubsub_topic,
          {:settings_updated, updated_settings}
        )

        {:noreply, %{state | settings: updated_settings}}

      {:error, reason} ->
        Logger.error("Failed to save settings: #{reason}")
        {:noreply, state}
    end
  end

  @impl true
  def handle_cast({:put, key, value}, state) do
    # Update the specific setting
    updated_settings = Map.put(state.settings, key, value)

    # Save immediately to disk
    case Storage.save(updated_settings) do
      {:ok, _path} ->
        Logger.info("Settings saved to disk")

        # Broadcast the update to all clients
        PubSub.broadcast(
          Mqttable.PubSub,
          @pubsub_topic,
          {:settings_updated, updated_settings}
        )

        {:noreply, %{state | settings: updated_settings}}

      {:error, reason} ->
        Logger.error("Failed to save settings: #{reason}")
        {:noreply, state}
    end
  end

  @impl true
  def terminate(_reason, state) do
    # Save settings when the server terminates
    case Storage.save(state.settings) do
      {:ok, _path} ->
        Logger.info("Settings saved on termination")

      {:error, reason} ->
        Logger.error("Failed to save settings on termination: #{reason}")
    end

    :ok
  end
end
