defmodule Mqttable.ConnectionSets.Server do
  @moduledoc """
  GenServer for managing connection sets state.
  Provides in-memory storage with periodic persistence to disk.
  Broadcasts changes to all connected clients.
  """
  use GenServer
  require Logger

  alias Mqttable.ConnectionSets.Storage
  alias Mqttable.Uploads.<PERSON><PERSON><PERSON><PERSON>
  alias Mqttable.Uploads.FileStorage
  alias Phoenix.PubSub

  # Save every 1 minutes
  @save_interval :timer.minutes(1)
  @pubsub_topic "brokers"

  # Run certificate cleanup every 60 save intervals (1 hour)
  @cleanup_interval 60
  # Run file cleanup every 120 save intervals (2 hours)
  @file_cleanup_interval 120

  # Client API

  @doc """
  Starts the connection sets server.
  """
  def start_link(opts \\ []) do
    GenServer.start_link(__MODULE__, opts, name: __MODULE__)
  end

  @doc """
  Retrieves all connection sets from state.
  """
  def fetch_all_connection_sets do
    GenServer.call(__MODULE__, :fetch_all_connection_sets)
  end

  @doc """
  Retrieves the current UI state.
  """
  def fetch_ui_state do
    GenServer.call(__MODULE__, :fetch_ui_state)
  end

  @doc """
  Applies connection sets updates with state change detection.
  """
  def apply_connection_sets_update(connection_sets) do
    GenServer.cast(__MODULE__, {:apply_connection_sets_update, connection_sets})
  end

  @doc """
  Applies UI state updates with change broadcasting.
  """
  def apply_ui_state_update(ui_state) do
    GenServer.cast(__MODULE__, {:apply_ui_state_update, ui_state})
  end

  @doc """
  Triggers immediate persistence to disk.
  """
  def persist_immediately do
    GenServer.cast(__MODULE__, :persist_immediately)
  end

  # Server Callbacks

  @impl true
  def init(_opts) do
    {connection_sets, ui_state} = load_initial_state()
    schedule_periodic_task()

    initial_state = %{
      connection_sets: connection_sets,
      ui_state: ui_state,
      dirty: false,
      save_counter: 0
    }

    {:ok, initial_state, {:continue, :restore_saved_connections}}
  end

  @impl true
  def handle_continue(:restore_saved_connections, state) do
    state
    |> restore_connections_from_state()
    |> then(&{:noreply, &1})
  end

  @impl true
  def handle_call(:fetch_all_connection_sets, _from, %{connection_sets: sets} = state) do
    {:reply, sets, state}
  end

  @impl true
  def handle_call(:fetch_ui_state, _from, %{ui_state: ui_state} = state) do
    {:reply, ui_state, state}
  end

  @impl true
  def handle_cast({:apply_connection_sets_update, connection_sets}, state) do
    state
    |> apply_connection_sets_change(connection_sets)
    |> then(&{:noreply, &1})
  end

  @impl true
  def handle_cast({:apply_ui_state_update, ui_state}, state) do
    state
    |> apply_ui_state_change(ui_state)
    |> then(&{:noreply, &1})
  end

  @impl true
  def handle_cast(:persist_immediately, state) do
    state
    |> persist_when_dirty()
    |> then(&{:noreply, &1})
  end

  @impl true
  def handle_info(:periodic_task, state) do
    state
    |> increment_save_counter()
    |> persist_when_dirty()
    |> perform_cleanup_tasks()
    |> tap(fn _ -> schedule_periodic_task() end)
    |> then(&{:noreply, &1})
  end

  @impl true
  def terminate(_reason, %{dirty: true} = state) do
    persist_state_data(state.connection_sets, state.ui_state)
    :ok
  end

  @impl true
  def terminate(_reason, _state), do: :ok

  # Private functions

  defp load_initial_state do
    Storage.load_data()
    |> handle_storage_load_result()
  end

  defp handle_storage_load_result({:ok, sets, ui_state}), do: {sets, ui_state}

  defp handle_storage_load_result({:error, reason}) do
    Logger.error("Failed to load connection sets: #{reason}")
    {[], %{expanded_sets: %{}}}
  end

  defp restore_connections_from_state(%{connection_sets: connection_sets} = state) do
    Logger.info("Starting saved connections after ConnectionSets server initialization")
    Mqttable.MqttClient.Manager.restore_all_saved_connections(connection_sets)
    state
  end

  defp apply_connection_sets_change(state, new_sets) do
    state
    |> maybe_broadcast_connection_sets_update(new_sets)
    |> Map.put(:connection_sets, new_sets)
  end

  defp maybe_broadcast_connection_sets_update(%{connection_sets: current_sets} = state, new_sets)
       when new_sets != current_sets do
    broadcast_connection_sets_update(new_sets)
    %{state | dirty: true}
  end

  defp maybe_broadcast_connection_sets_update(state, _new_sets), do: state

  defp broadcast_connection_sets_update(connection_sets) do
    PubSub.broadcast(
      Mqttable.PubSub,
      @pubsub_topic,
      {:connection_sets_updated, connection_sets}
    )
  end

  defp apply_ui_state_change(state, new_ui_state) do
    state
    |> maybe_broadcast_ui_state_update(new_ui_state)
    |> Map.put(:ui_state, new_ui_state)
  end

  defp maybe_broadcast_ui_state_update(%{ui_state: current_ui_state} = state, new_ui_state)
       when new_ui_state != current_ui_state do
    broadcast_ui_state_update(new_ui_state)
    %{state | dirty: true}
  end

  defp maybe_broadcast_ui_state_update(%{dirty: dirty} = state, _new_ui_state) do
    %{state | dirty: dirty}
  end

  defp broadcast_ui_state_update(ui_state) do
    PubSub.broadcast(
      Mqttable.PubSub,
      @pubsub_topic,
      {:ui_state_updated, ui_state}
    )
  end

  defp persist_when_dirty(%{dirty: true} = state) do
    persist_state_data(state.connection_sets, state.ui_state)
    %{state | dirty: false}
  end

  defp persist_when_dirty(state), do: state

  defp increment_save_counter(%{save_counter: counter} = state) do
    %{state | save_counter: counter + 1}
  end

  defp perform_cleanup_tasks(%{save_counter: counter} = state)
       when counter >= @file_cleanup_interval do
    perform_full_cleanup(state)
    %{state | save_counter: 0}
  end

  defp perform_cleanup_tasks(%{save_counter: counter} = state)
       when rem(counter, @cleanup_interval) == 0 do
    perform_certificate_cleanup(state)
    state
  end

  defp perform_cleanup_tasks(state), do: state

  defp perform_full_cleanup(%{connection_sets: sets, ui_state: ui_state}) do
    FileStorage.cleanup_unused_files(sets, ui_state)
    CertificateHandler.cleanup_unused_certificates(sets)
  end

  defp perform_certificate_cleanup(%{connection_sets: sets}) do
    CertificateHandler.cleanup_unused_certificates(sets)
  end

  defp persist_state_data(connection_sets, ui_state) do
    Storage.persist_data(connection_sets, ui_state)
    |> log_persistence_result()
  end

  defp log_persistence_result({:ok, _path}) do
    Logger.info("Connection sets and UI state saved to disk")
    :ok
  end

  defp log_persistence_result({:error, reason}) do
    Logger.error("Failed to save data: #{reason}")
    :error
  end

  defp schedule_periodic_task do
    Process.send_after(self(), :periodic_task, @save_interval)
  end
end
