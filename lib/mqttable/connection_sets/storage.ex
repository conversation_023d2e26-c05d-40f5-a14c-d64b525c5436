defmodule Mqttable.ConnectionSets.Storage do
  @moduledoc """
  Module for handling the persistence of connection sets to disk.
  """

  require Logger

  @data_dir "priv/data"
  @connection_sets_file Path.join(@data_dir, "brokers.json")
  @ui_state_file Path.join(@data_dir, "ui_state.json")

  @doc """
  Persists connection sets and UI state to JSON files.

  ## Parameters

  - `connection_sets`: List of connection set maps to be saved
  - `ui_state`: Map containing UI state information

  ## Returns

  - `{:ok, path}`: On successful save, returns the path where the files were saved
  - `{:error, reason}`: On failure, returns the error reason
  """
  def persist_data(connection_sets, ui_state \\ %{expanded_sets: %{}})
      when is_list(connection_sets) and is_map(ui_state) do
    ensure_data_directory()

    with {:ok, _} <- write_connection_sets(connection_sets),
         {:ok, _} <- write_ui_state(ui_state) do
      {:ok, @data_dir}
    end
  end

  @doc """
  Loads connection sets and UI state from JSON files.

  ## Returns

  - `{:ok, connection_sets, ui_state}`: On successful load, returns the list of connection sets and UI state
  - `{:error, reason}`: On failure, returns the error reason
  """
  def load_data do
    with {:ok, connection_sets} <- read_connection_sets(),
         {:ok, ui_state} <- read_ui_state() do
      {:ok, connection_sets, ui_state}
    end
  end

  # Private functions

  defp ensure_data_directory do
    File.mkdir_p!(@data_dir)
  end

  defp write_connection_sets(connection_sets) do
    connection_sets
    |> encode_to_json()
    |> then(&write_file_with_error_handling(@connection_sets_file, &1, "connection sets"))
  end

  defp write_ui_state(ui_state) do
    ui_state
    |> normalize_ui_state_for_persistence()
    |> encode_to_json()
    |> then(&write_file_with_error_handling(@ui_state_file, &1, "UI state"))
  end

  defp normalize_ui_state_for_persistence(ui_state) do
    ui_state
    |> normalize_field_broker_names(:expanded_sets)
    |> normalize_field_broker_names(:send_modal_forms)
    |> normalize_field_broker_names(:mqtt5_properties_collapsed)
    |> normalize_field_broker_names(:trace_filters)
  end

  defp encode_to_json(data) do
    case Jason.encode(data, pretty: true) do
      {:ok, json} -> {:ok, json}
      {:error, reason} -> {:error, "JSON encoding failed: #{inspect(reason)}"}
    end
  end

  defp write_file_with_error_handling(file_path, {:ok, content}, file_type) do
    case File.write(file_path, content) do
      :ok -> {:ok, file_path}
      {:error, reason} -> {:error, "Failed to write #{file_type} file: #{reason}"}
    end
  end

  defp write_file_with_error_handling(_file_path, {:error, reason}, _file_type) do
    {:error, reason}
  end

  defp read_connection_sets do
    @connection_sets_file
    |> file_exists_and_readable?()
    |> read_and_parse_connection_sets()
  end

  defp file_exists_and_readable?(file_path) do
    {File.exists?(file_path), file_path}
  end

  defp read_and_parse_connection_sets({true, file_path}) do
    with {:ok, json} <- File.read(file_path),
         {:ok, raw_data} <- Jason.decode(json),
         parsed_sets <- transform_connection_sets_for_runtime(raw_data) do
      {:ok, parsed_sets}
    else
      {:error, reason} when is_atom(reason) ->
        {:error, "Failed to read connection sets file: #{reason}"}

      {:error, %Jason.DecodeError{} = reason} ->
        {:error, "Failed to decode connection sets JSON: #{inspect(reason)}"}
    end
  end

  defp read_and_parse_connection_sets({false, _file_path}) do
    {:ok, []}
  end

  defp transform_connection_sets_for_runtime(raw_sets) do
    Enum.map(raw_sets, &transform_connection_set/1)
  end

  defp transform_connection_set(raw_set) do
    raw_set
    |> string_keys_to_atoms()
    |> transform_nested_variables()
    |> transform_nested_connections()
  end

  defp string_keys_to_atoms(map) when is_map(map) do
    for {key, val} <- map, into: %{}, do: {String.to_atom(key), val}
  end

  defp transform_nested_variables(%{variables: variables} = set) when is_list(variables) do
    %{set | variables: Enum.map(variables, &string_keys_to_atoms/1)}
  end

  defp transform_nested_variables(set), do: Map.put(set, :variables, [])

  defp transform_nested_connections(%{connections: connections} = set)
       when is_list(connections) do
    %{set | connections: Enum.map(connections, &transform_connection/1)}
  end

  defp transform_nested_connections(set), do: Map.put(set, :connections, [])

  defp transform_connection(raw_conn) do
    raw_conn
    |> string_keys_to_atoms()
    |> transform_connection_topics()
    |> transform_connection_scheduled_messages()
    |> transform_connection_user_properties()
  end

  defp transform_connection_topics(%{topics: topics} = conn) when is_list(topics) do
    %{conn | topics: Enum.map(topics, &transform_topic_item/1)}
  end

  defp transform_connection_topics(conn), do: Map.put(conn, :topics, [])

  defp transform_topic_item(topic) when is_map(topic), do: string_keys_to_atoms(topic)
  defp transform_topic_item(topic) when is_binary(topic), do: topic
  defp transform_topic_item(topic), do: topic

  defp transform_connection_scheduled_messages(%{scheduled_messages: messages} = conn)
       when is_list(messages) do
    %{conn | scheduled_messages: Enum.map(messages, &transform_scheduled_message_item/1)}
  end

  defp transform_connection_scheduled_messages(conn), do: Map.put(conn, :scheduled_messages, [])

  defp transform_scheduled_message_item(msg) when is_map(msg), do: string_keys_to_atoms(msg)
  defp transform_scheduled_message_item(msg), do: msg

  defp transform_connection_user_properties(%{user_properties: properties} = conn)
       when is_list(properties) do
    %{conn | user_properties: Enum.map(properties, &transform_user_property_item/1)}
  end

  defp transform_connection_user_properties(conn), do: Map.put(conn, :user_properties, [])

  defp transform_user_property_item(prop) when is_map(prop), do: string_keys_to_atoms(prop)
  defp transform_user_property_item(prop), do: prop

  defp read_ui_state do
    @ui_state_file
    |> file_exists_and_readable?()
    |> read_and_parse_ui_state()
  end

  defp read_and_parse_ui_state({true, file_path}) do
    with {:ok, json} <- File.read(file_path),
         {:ok, ui_state} <- Jason.decode(json, keys: :atoms),
         normalized_state <- normalize_ui_state_for_runtime(ui_state) do
      {:ok, normalized_state}
    else
      {:error, %Jason.DecodeError{} = reason} ->
        {:error, "Failed to decode UI state JSON: #{inspect(reason)}"}

      {:error, reason} ->
        {:error, "Failed to read UI state file: #{reason}"}
    end
  end

  defp read_and_parse_ui_state({false, _file_path}) do
    {:ok, %{expanded_sets: %{}}}
  end

  defp normalize_ui_state_for_runtime(ui_state) do
    ui_state
    |> convert_nested_keys_to_atoms()
    |> normalize_field_broker_names(:expanded_sets)
    |> normalize_field_broker_names(:send_modal_forms)
    |> normalize_field_broker_names(:mqtt5_properties_collapsed)
    |> normalize_field_broker_names(:trace_filters)
  end

  defp normalize_field_broker_names(ui_state, field_key) do
    field_data = Map.get(ui_state, field_key, %{})

    normalized_data =
      if is_map(field_data) do
        field_data
        |> Enum.map(fn {key, value} -> {to_string(key), value} end)
        |> Enum.into(%{})
      else
        field_data
      end

    Map.put(ui_state, field_key, normalized_data)
  end

  defp convert_nested_keys_to_atoms(map) when is_map(map) do
    Map.new(map, fn
      {:name, value} -> {:name, value}
      {"name", value} -> {:name, value}
      {key, value} when is_map(value) -> {key, convert_nested_keys_to_atoms(value)}
      {key, value} when is_list(value) -> {key, Enum.map(value, &convert_nested_value_keys/1)}
      {key, value} -> {key, value}
    end)
  end

  defp convert_nested_keys_to_atoms(list) when is_list(list) do
    Enum.map(list, &convert_nested_value_keys/1)
  end

  defp convert_nested_value_keys(value) when is_map(value) do
    value
    |> preserve_name_field()
    |> convert_nested_keys_to_atoms()
  end

  defp convert_nested_value_keys(value) when is_list(value) do
    Enum.map(value, &convert_nested_value_keys/1)
  end

  defp convert_nested_value_keys(value), do: value

  defp preserve_name_field(value) when is_map(value) do
    name = Map.get(value, :name) || Map.get(value, "name")

    value
    |> convert_nested_keys_to_atoms()
    |> then(fn converted ->
      if name, do: Map.put(converted, :name, name), else: converted
    end)
  end

  defp preserve_name_field(value), do: value
end
