defmodule Mqttable.Templating.Engine do
  @moduledoc """
  Core template engine for processing Liquid templates with MQTT-specific custom functions.

  This module provides the main interface for template parsing, validation, and rendering
  with support for custom functions commonly used in IoT and MQTT scenarios.
  """

  alias Mqttable.Templating.Functions
  require Logger

  defmodule DynamicContext do
    @moduledoc """
    A dynamic context that generates fresh function values on each access.

    This ensures that multiple calls to the same function in a template
    (e.g., {{ timezone }} {{ timezone }}) return different values each time.
    """

    @behaviour Access

    defstruct [:base_context, :function_names]

    @type t :: %__MODULE__{
            base_context: map(),
            function_names: MapSet.t()
          }

    def new(base_context \\ %{}) do
      function_names = Functions.list_function_names() |> MapSet.new()

      %__MODULE__{
        base_context: base_context,
        function_names: function_names
      }
    end

    @impl Access
    def fetch(%__MODULE__{base_context: base, function_names: functions}, key) do
      cond do
        # First check if it's in the base context
        Map.has_key?(base, key) ->
          Map.fetch(base, key)

        # Then check if it's a function name and generate fresh value
        MapSet.member?(functions, key) ->
          {:ok, call_template_function(key)}

        # Not found
        true ->
          :error
      end
    end

    @impl Access
    def get_and_update(%__MODULE__{} = context, key, fun) do
      current =
        case fetch(context, key) do
          {:ok, value} -> value
          :error -> nil
        end

      case fun.(current) do
        {get_value, new_value} ->
          new_context = %{context | base_context: Map.put(context.base_context, key, new_value)}
          {get_value, new_context}

        :pop ->
          new_context = %{context | base_context: Map.delete(context.base_context, key)}
          {current, new_context}
      end
    end

    @impl Access
    def pop(%__MODULE__{} = context, key) do
      case fetch(context, key) do
        {:ok, value} ->
          new_context = %{context | base_context: Map.delete(context.base_context, key)}
          {value, new_context}

        :error ->
          {nil, context}
      end
    end

    # Call template function by name
    defp call_template_function(function_name) do
      case function_name do
        "timezone" -> Functions.timezone()
        _ -> apply(Functions, String.to_atom(function_name), [])
      end
    rescue
      _ -> nil
    end
  end

  # Replace function calls in template with unique placeholders
  defp replace_functions_with_unique_placeholders(template) do
    function_names = Functions.list_function_names()

    # First pass: handle parameterized function calls
    {template_after_params, param_map, param_values, param_counter} =
      replace_parameterized_functions(template, function_names)

    # Second pass: handle simple function calls (existing logic)
    {final_template, placeholder_map, placeholder_values, _counter} =
      function_names
      |> Enum.reduce(
        {template_after_params, param_map, param_values, param_counter},
        fn function_name, {current_template, acc_map, acc_values, counter} ->
          # Find all occurrences of this function in the template (both standalone and in pipes)
          # Pattern 1: {{ function_name }}
          standalone_pattern = "{{ #{function_name} }}"
          # Pattern 2: {{ function_name | ... }} (function at start of pipe)
          pipe_pattern = ~r/\{\{\s*#{Regex.escape(function_name)}\s*\|/

          # Replace standalone occurrences
          {temp_template, temp_map, temp_values, temp_counter} =
            replace_function_occurrences(
              current_template,
              standalone_pattern,
              function_name,
              acc_map,
              acc_values,
              counter
            )

          # Replace pipe occurrences
          {new_template, new_map, new_values, new_counter} =
            replace_pipe_function_occurrences(
              temp_template,
              pipe_pattern,
              function_name,
              temp_map,
              temp_values,
              temp_counter
            )

          {new_template, new_map, new_values, new_counter}
        end
      )

    {final_template, placeholder_map, placeholder_values}
  end

  # Replace all occurrences of a function pattern with unique placeholders
  defp replace_function_occurrences(
         template,
         pattern,
         function_name,
         placeholder_map,
         placeholder_values,
         counter
       ) do
    case String.contains?(template, pattern) do
      false ->
        {template, placeholder_map, placeholder_values, counter}

      true ->
        # Create unique placeholder
        unique_placeholder = "__UNIQUE_#{function_name}_#{counter}__"

        # Replace first occurrence
        new_template = String.replace(template, pattern, unique_placeholder, global: false)

        # Add to placeholder map
        new_map = Map.put(placeholder_map, unique_placeholder, function_name)

        # Generate function value and add to values map
        function_value = call_function(function_name)
        new_values = Map.put(placeholder_values, unique_placeholder, function_value)

        # Recursively replace remaining occurrences
        replace_function_occurrences(
          new_template,
          pattern,
          function_name,
          new_map,
          new_values,
          counter + 1
        )
    end
  end

  # Replace function calls in pipe operations with unique placeholders
  defp replace_pipe_function_occurrences(
         template,
         pipe_pattern,
         function_name,
         placeholder_map,
         placeholder_values,
         counter
       ) do
    case Regex.run(pipe_pattern, template) do
      nil ->
        {template, placeholder_map, placeholder_values, counter}

      [match] ->
        # Create unique placeholder
        unique_placeholder = "__UNIQUE_#{function_name}_#{counter}__"

        # Replace the function name in the match with the placeholder
        new_match = String.replace(match, function_name, unique_placeholder)

        # Replace the match in the template
        new_template = String.replace(template, match, new_match, global: false)

        # Add to placeholder map
        new_map = Map.put(placeholder_map, unique_placeholder, function_name)

        # Generate function value and add to values map
        function_value = call_function(function_name)
        new_values = Map.put(placeholder_values, unique_placeholder, function_value)

        # Recursively replace remaining occurrences
        replace_pipe_function_occurrences(
          new_template,
          pipe_pattern,
          function_name,
          new_map,
          new_values,
          counter + 1
        )
    end
  end

  # Replace parameterized function calls with unique placeholders
  defp replace_parameterized_functions(template, function_names) do
    # Pattern to match: {{ function_name(parameters) }}
    parameterized_pattern = ~r/\{\{\s*(\w+)\s*\(\s*(.+?)\s*\)\s*\}\}/

    {final_template, placeholder_map, placeholder_values, counter} =
      Regex.scan(parameterized_pattern, template, capture: :all_but_first)
      |> Enum.with_index()
      |> Enum.reduce({template, %{}, %{}, 0}, fn {[function_name, params_str], _index},
                                                 {current_template, acc_map, acc_values, counter} ->
        # Only process if it's a known function
        if function_name in function_names do
          # Create unique placeholder
          unique_placeholder = "__PARAM_#{function_name}_#{counter}__"

          # Parse parameters
          parsed_params = parse_function_parameters(params_str)

          # Generate function value with parameters
          function_value = call_function_with_params(function_name, parsed_params)

          # Create the full match pattern to replace
          full_match = "{{ #{function_name}(#{params_str}) }}"

          # Replace the match in the template with a Liquid variable placeholder
          liquid_placeholder = "{{ #{unique_placeholder} }}"

          new_template =
            String.replace(current_template, full_match, liquid_placeholder, global: false)

          # Add to maps
          new_map = Map.put(acc_map, unique_placeholder, function_name)
          new_values = Map.put(acc_values, unique_placeholder, function_value)

          {new_template, new_map, new_values, counter + 1}
        else
          {current_template, acc_map, acc_values, counter}
        end
      end)

    {final_template, placeholder_map, placeholder_values, counter}
  end

  # Replace unique placeholders with fresh function values
  defp replace_unique_placeholders_with_values(rendered_string, placeholder_map) do
    placeholder_map
    |> Enum.reduce(rendered_string, fn {placeholder, function_name}, acc ->
      # Replace this specific placeholder with a fresh function call
      replacement = get_placeholder_replacement(placeholder, function_name)
      String.replace(acc, placeholder, replacement)
    end)
  end

  defp get_placeholder_replacement(placeholder, function_name) do
    # Only call function again if it's not a parameterized function
    # Parameterized functions (with __PARAM_ prefix) already have their values computed
    if String.starts_with?(placeholder, "__PARAM_") do
      # For parameterized functions, the value should already be in the rendered string
      # This shouldn't happen if the logic is correct, but let's be safe
      placeholder
    else
      case call_function(function_name) do
        # Keep placeholder if function fails
        nil -> placeholder
        value -> to_string(value)
      end
    end
  end

  # Helper function to call individual template functions
  defp call_function(function_name) do
    case function_name do
      # IoT/Sensor data functions
      "temperature" -> Functions.temperature()
      "humidity" -> Functions.humidity()
      "pressure" -> Functions.pressure()
      "battery_level" -> Functions.battery_level()
      "signal_strength" -> Functions.signal_strength()
      "device_status" -> Functions.device_status()
      "device_id" -> Functions.device_id()
      "firmware_version" -> Functions.firmware_version()
      "uptime" -> Functions.uptime()
      "mac_address" -> Functions.mac_address()
      "ip_address" -> Functions.ip_address()
      "now" -> Functions.now()
      "iso8601" -> Functions.iso8601()
      "unix_timestamp" -> Functions.unix_timestamp()
      "random_int" -> Functions.random_int()
      "random_float" -> Functions.random_float()
      "random_string" -> Functions.random_string()
      "random_bool" -> Functions.random_bool()
      "uuid" -> Functions.uuid()
      "light_level" -> Functions.light_level()
      "air_quality" -> Functions.air_quality()
      "timestamp" -> Functions.timestamp()
      "random_choice" -> Functions.random_choice(["option1", "option2", "option3"])
      "random_hex" -> Functions.random_hex()
      # Faker-based functions - generate fresh values each time
      "name" -> Functions.name()
      "first_name" -> Functions.first_name()
      "last_name" -> Functions.last_name()
      "title" -> Functions.title()
      "prefix" -> Functions.prefix()
      "suffix" -> Functions.suffix()
      "address" -> Functions.address()
      "city" -> Functions.city()
      "country" -> Functions.country()
      "country_code" -> Functions.country_code()
      "state" -> Functions.state()
      "postcode" -> Functions.postcode()
      "latitude" -> Functions.latitude()
      "longitude" -> Functions.longitude()
      "timezone" -> Functions.timezone()
      "geohash" -> Functions.geohash()
      "company" -> Functions.company()
      "company_suffix" -> Functions.company_suffix()
      "buzzword" -> Functions.buzzword()
      "catch_phrase" -> Functions.catch_phrase()
      "email" -> Functions.email()
      "username" -> Functions.username()
      "domain" -> Functions.domain()
      "url" -> Functions.url()
      "ipv4" -> Functions.ipv4()
      "ipv6" -> Functions.ipv6()
      "user_agent" -> Functions.user_agent()
      "sentence" -> Functions.sentence()
      "paragraph" -> Functions.paragraph()
      "word" -> Functions.word()
      "words" -> Functions.words()
      "markdown" -> Functions.markdown()
      "markdown_headers" -> Functions.markdown_headers()
      "markdown_emphasis" -> Functions.markdown_emphasis()
      "markdown_inline_code" -> Functions.markdown_inline_code()
      "markdown_block_code" -> Functions.markdown_block_code()
      "markdown_ordered_list" -> Functions.markdown_ordered_list()
      "markdown_unordered_list" -> Functions.markdown_unordered_list()
      "markdown_table" -> Functions.markdown_table()
      "product_name" -> Functions.product_name()
      "price" -> Functions.price()
      "color_name" -> Functions.color_name()
      "rgb_hex" -> Functions.rgb_hex()
      "department" -> Functions.department()
      "filename" -> Functions.filename()
      "file_extension" -> Functions.file_extension()
      "mime_type" -> Functions.mime_type()
      "dish" -> Functions.dish()
      "ingredient" -> Functions.ingredient()
      "spice" -> Functions.spice()
      "vehicle" -> Functions.vehicle()
      "vehicle_make" -> Functions.vehicle_make()
      "vehicle_model" -> Functions.vehicle_model()
      "vin" -> Functions.vin()
      "currency_code" -> Functions.currency_code()
      "currency_symbol" -> Functions.currency_symbol()
      "iban" -> Functions.iban()
      "date_of_birth" -> Functions.date_of_birth()
      "past_date" -> Functions.past_date()
      "future_date" -> Functions.future_date()
      "avatar_url" -> Functions.avatar_url()
      "image_url" -> Functions.image_url()
      "bitcoin_address" -> Functions.bitcoin_address()
      "ethereum_address" -> Functions.ethereum_address()
      "ethereum_signature" -> Functions.ethereum_signature()
      "date_format" -> Functions.date_format()
      "timezone_convert" -> Functions.timezone_convert()
      # Default case
      _ -> nil
    end
  end

  # Helper function to call template functions with parameters
  defp call_function_with_params(function_name, params) do
    case {function_name, params} do
      # Handle random_choice with array parameter
      {"random_choice", [choices]} when is_list(choices) and length(choices) > 0 ->
        Functions.random_choice(choices)

      # Handle random_choice with empty parameters (fallback to default)
      {"random_choice", []} ->
        Functions.random_choice(["option1", "option2", "option3"])

      # Handle other parameterized functions here as needed
      # {"random_int", [min, max]} when is_number(min) and is_number(max) ->
      #   Functions.random_int(min, max)

      # Fallback to calling function without parameters
      {function_name, _} ->
        call_function(function_name)
    end
  rescue
    _ -> nil
  end

  # Parse function parameters from string format
  defp parse_function_parameters(params_str) do
    try do
      # Clean up the parameter string
      cleaned_params = String.trim(params_str)

      # Handle array parameters like ["item1", "item2", "item3"]
      cond do
        # Array parameter
        String.starts_with?(cleaned_params, "[") and String.ends_with?(cleaned_params, "]") ->
          parse_array_parameter(cleaned_params)

        # Single string parameter
        String.starts_with?(cleaned_params, "\"") and String.ends_with?(cleaned_params, "\"") ->
          [String.slice(cleaned_params, 1..-2//1)]

        # Single number parameter
        is_number_string?(cleaned_params) ->
          [parse_number(cleaned_params)]

        # Boolean parameter
        cleaned_params in ["true", "false"] ->
          [cleaned_params == "true"]

        # Default: treat as string
        true ->
          [cleaned_params]
      end
    rescue
      _ -> []
    end
  end

  # Parse array parameter from string like ["item1", "item2", "item3"]
  defp parse_array_parameter(array_str) do
    try do
      # Remove brackets and split by comma
      inner_content = String.slice(array_str, 1..-2//1)

      if String.trim(inner_content) == "" do
        [[]]
      else
        items =
          inner_content
          |> String.split(",")
          |> Enum.map(&parse_array_item/1)
          |> Enum.reject(&is_nil/1)

        [items]
      end
    rescue
      _ -> [[]]
    end
  end

  # Parse individual array item
  defp parse_array_item(item_str) do
    cleaned_item = String.trim(item_str)

    cond do
      # String item with quotes
      String.starts_with?(cleaned_item, "\"") and String.ends_with?(cleaned_item, "\"") ->
        String.slice(cleaned_item, 1..-2//1)

      # Number item
      is_number_string?(cleaned_item) ->
        parse_number(cleaned_item)

      # Boolean item
      cleaned_item in ["true", "false"] ->
        cleaned_item == "true"

      # Default: treat as string without quotes
      true ->
        cleaned_item
    end
  rescue
    _ -> nil
  end

  # Check if string represents a number
  defp is_number_string?(str) do
    case Float.parse(str) do
      {_, ""} -> true
      _ -> false
    end
  end

  # Parse number from string
  defp parse_number(str) do
    case Float.parse(str) do
      {num, ""} ->
        if String.contains?(str, ".") do
          num
        else
          trunc(num)
        end

      _ ->
        0
    end
  end

  @doc """
  Renders a Liquid template with the provided context and variables.

  ## Parameters
  - `template` - The Liquid template string
  - `context` - Base context data (map)
  - `variables` - Template-specific variables (map)

  ## Returns
  - `{:ok, rendered_string}` on success
  - `{:error, reason}` on failure

  ## Examples

      iex> template = "Hello {{ name }}, temperature is {{ temperature }}°C"
      iex> context = %{"name" => "World"}
      iex> Mqttable.Templating.Engine.render(template, context, %{})
      {:ok, "Hello World, temperature is 22.5°C"}
  """
  @spec render(String.t(), map(), map()) :: {:ok, String.t()} | {:error, String.t()}
  def render(template, context \\ %{}, variables \\ %{}) do
    try do
      # Create base context
      base_context = Map.merge(context, variables)

      # First pass: replace function calls with unique placeholders
      {template_with_placeholders, placeholder_map, placeholder_values} =
        replace_functions_with_unique_placeholders(template)

      # Merge placeholder values into the context
      enhanced_context = Map.merge(base_context, placeholder_values)

      # Parse and render template with custom filters
      case Solid.parse(template_with_placeholders) do
        {:ok, parsed_template} ->
          result =
            parsed_template
            |> Solid.render!(enhanced_context, custom_filters: Functions)
            |> to_string()
            |> replace_unique_placeholders_with_values(placeholder_map)

          {:ok, result}

        {:error, reason} ->
          {:error, format_parse_error(reason)}
      end
    rescue
      error ->
        Logger.warning("Template rendering failed: #{inspect(error)}")
        {:error, "Template rendering failed: #{Exception.message(error)}"}
    end
  end

  @doc """
  Validates a Liquid template for syntax errors.

  ## Parameters
  - `template` - The Liquid template string to validate

  ## Returns
  - `:ok` if template is valid
  - `{:error, reason}` if template has syntax errors
  """
  @spec validate(String.t()) :: :ok | {:error, String.t()}
  def validate(template) do
    try do
      case Solid.parse(template) do
        {:ok, _parsed_template} ->
          :ok

        {:error, reason} ->
          {:error, format_parse_error(reason)}
      end
    rescue
      error ->
        {:error, "Template validation failed: #{Exception.message(error)}"}
    end
  end

  @doc """
  Renders a template preview with sample data for testing purposes.

  This function generates fresh function values on each call to provide
  real-time preview updates without caching.

  ## Parameters
  - `template` - The Liquid template string
  - `variables` - Template-specific variables (map)

  ## Returns
  - `{:ok, rendered_string}` on success
  - `{:error, reason}` on failure
  """
  @spec preview(String.t(), map()) :: {:ok, String.t()} | {:error, String.t()}
  def preview(template, variables \\ %{}) do
    sample_context = get_sample_context()
    render(template, sample_context, variables)
  end

  @doc """
  Renders a template with fresh function values (no caching).

  This is used for preview functionality where we want fresh values
  on each render to show real-time updates.

  Note: With the new DynamicContext implementation, this function
  now behaves the same as render/3 since all function calls generate
  fresh values automatically.

  ## Parameters
  - `template` - The Liquid template string
  - `context` - Base context data (map)
  - `variables` - Template-specific variables (map)

  ## Returns
  - `{:ok, rendered_string}` on success
  - `{:error, reason}` on failure
  """
  @spec render_with_fresh_functions(String.t(), map(), map()) ::
          {:ok, String.t()} | {:error, String.t()}
  def render_with_fresh_functions(template, context \\ %{}, variables \\ %{}) do
    # With DynamicContext, all renders use fresh function values
    render(template, context, variables)
  end

  @doc """
  Extracts variable placeholders from a template.

  This function analyzes a template and returns a list of variable names
  that are used but not provided by the custom functions.

  ## Parameters
  - `template` - The Liquid template string

  ## Returns
  - `{:ok, [variable_names]}` on success
  - `{:error, reason}` on failure
  """
  @spec extract_variables(String.t()) :: {:ok, [String.t()]} | {:error, String.t()}
  def extract_variables(template) do
    try do
      case Solid.parse(template) do
        {:ok, _parsed_template} ->
          # This is a simplified implementation
          # In a more sophisticated version, we would traverse the AST
          variables =
            template
            |> extract_liquid_variables()
            |> filter_custom_functions()

          {:ok, variables}

        {:error, reason} ->
          {:error, format_parse_error(reason)}
      end
    rescue
      error ->
        {:error, "Variable extraction failed: #{Exception.message(error)}"}
    end
  end

  @doc """
  Compiles a template for better performance when rendering multiple times.

  ## Parameters
  - `template` - The Liquid template string

  ## Returns
  - `{:ok, compiled_template}` on success
  - `{:error, reason}` on failure
  """
  @spec compile(String.t()) :: {:ok, any()} | {:error, String.t()}
  def compile(template) do
    try do
      case Solid.parse(template) do
        {:ok, parsed_template} ->
          {:ok, parsed_template}

        {:error, reason} ->
          {:error, format_parse_error(reason)}
      end
    rescue
      error ->
        {:error, "Template compilation failed: #{Exception.message(error)}"}
    end
  end

  @doc """
  Renders a pre-compiled template.

  ## Parameters
  - `compiled_template` - Previously compiled template
  - `context` - Base context data (map)
  - `variables` - Template-specific variables (map)

  ## Returns
  - `{:ok, rendered_string}` on success
  - `{:error, reason}` on failure
  """
  @spec render_compiled(any(), map(), map()) :: {:ok, String.t()} | {:error, String.t()}
  def render_compiled(compiled_template, context \\ %{}, variables \\ %{}) do
    try do
      # Merge context and variables
      merged_context = Map.merge(context, variables)

      result =
        compiled_template
        |> Solid.render!(merged_context, custom_filters: Functions)
        |> to_string()

      {:ok, result}
    rescue
      error ->
        Logger.warning("Compiled template rendering failed: #{inspect(error)}")
        {:error, "Template rendering failed: #{Exception.message(error)}"}
    end
  end

  # Private helper functions

  defp format_parse_error(reason) when is_binary(reason), do: reason
  defp format_parse_error(reason), do: inspect(reason)

  defp get_sample_context do
    %{
      "device_id" => "sample_device_001",
      "client_id" => "mqttable_client",
      "broker_name" => "sample_broker",
      "topic" => "sensor/temperature",
      "timestamp" => DateTime.utc_now() |> DateTime.to_iso8601(),
      "user" => %{
        "name" => "Sample User",
        "id" => "user_123"
      }
    }
  end

  defp extract_liquid_variables(template) do
    # Simple regex to extract {{ variable }} patterns
    # This is a basic implementation - a more sophisticated version would parse the AST
    Regex.scan(~r/\{\{\s*([a-zA-Z_][a-zA-Z0-9_\.]*)\s*\}\}/, template, capture: :all_but_first)
    |> Enum.map(&List.first/1)
    |> Enum.uniq()
  end

  defp filter_custom_functions(variables) do
    # Filter out known custom function names
    custom_function_names = Functions.list_function_names()

    Enum.reject(variables, fn var ->
      # Check if variable looks like a function call
      String.contains?(var, "(") || var in custom_function_names
    end)
  end
end
