defmodule Mqttable.Templating.Validator do
  @moduledoc """
  Template validation utilities for ensuring template correctness and security.

  This module provides comprehensive validation for Liquid templates including
  syntax validation, security checks, and performance analysis.
  """

  alias Mqttable.Templating.{Engine, Functions}
  require Logger

  @doc """
  Performs comprehensive validation of a template.

  ## Parameters
  - `template` - The Liquid template string to validate
  - `options` - Validation options (optional)

  ## Returns
  - `{:ok, validation_result}` - Validation passed with details
  - `{:error, validation_errors}` - Validation failed with error details
  """
  @spec validate_template(String.t(), keyword()) :: {:ok, map()} | {:error, [map()]}
  def validate_template(template, _options \\ []) do
    validations = [
      &validate_syntax/1,
      &validate_security/1,
      &validate_performance/1,
      &validate_functions/1
    ]

    results =
      validations
      |> Enum.map(fn validator -> validator.(template) end)
      |> Enum.reduce({[], []}, fn
        {:ok, result}, {oks, errors} -> {[result | oks], errors}
        {:error, error}, {oks, errors} -> {oks, [error | errors]}
      end)

    case results do
      {validation_results, []} ->
        {:ok,
         %{
           valid: true,
           results: Enum.reverse(validation_results),
           warnings: extract_warnings(validation_results)
         }}

      {_, errors} ->
        {:error, Enum.reverse(errors)}
    end
  end

  @doc """
  Validates template syntax using the Solid parser.

  ## Parameters
  - `template` - The template string to validate

  ## Returns
  - `{:ok, result}` - Syntax is valid
  - `{:error, error}` - Syntax errors found
  """
  @spec validate_syntax(String.t()) :: {:ok, map()} | {:error, map()}
  def validate_syntax(template) do
    case Engine.validate(template) do
      :ok ->
        {:ok,
         %{
           type: :syntax,
           status: :valid,
           message: "Template syntax is valid"
         }}

      {:error, reason} ->
        {:error,
         %{
           type: :syntax,
           status: :error,
           message: "Syntax error: #{reason}",
           details: parse_syntax_error(reason)
         }}
    end
  end

  @doc """
  Validates template for security issues.

  This checks for potentially dangerous patterns that could be exploited.

  ## Parameters
  - `template` - The template string to validate

  ## Returns
  - `{:ok, result}` - No security issues found
  - `{:error, error}` - Security issues detected
  """
  @spec validate_security(String.t()) :: {:ok, map()} | {:error, map()}
  def validate_security(template) do
    security_checks = [
      &check_file_access/1,
      &check_system_calls/1,
      &check_infinite_loops/1,
      &check_excessive_nesting/1
    ]

    issues =
      security_checks
      |> Enum.flat_map(fn check -> check.(template) end)

    if Enum.empty?(issues) do
      {:ok,
       %{
         type: :security,
         status: :valid,
         message: "No security issues detected"
       }}
    else
      {:error,
       %{
         type: :security,
         status: :error,
         message: "Security issues detected",
         issues: issues
       }}
    end
  end

  @doc """
  Validates template for performance issues.

  ## Parameters
  - `template` - The template string to validate

  ## Returns
  - `{:ok, result}` - Performance looks good
  - `{:error, error}` - Performance issues detected
  """
  @spec validate_performance(String.t()) :: {:ok, map()} | {:error, map()}
  def validate_performance(template) do
    performance_checks = [
      &check_template_complexity/1,
      &check_loop_complexity/1,
      &check_function_usage/1
    ]

    warnings =
      performance_checks
      |> Enum.flat_map(fn check -> check.(template) end)

    {:ok,
     %{
       type: :performance,
       status: if(Enum.empty?(warnings), do: :valid, else: :warning),
       message:
         if(Enum.empty?(warnings), do: "No performance issues", else: "Performance warnings"),
       warnings: warnings
     }}
  end

  @doc """
  Validates that all functions used in the template are available.

  ## Parameters
  - `template` - The template string to validate

  ## Returns
  - `{:ok, result}` - All functions are available
  - `{:error, error}` - Unknown functions detected
  """
  @spec validate_functions(String.t()) :: {:ok, map()} | {:error, map()}
  def validate_functions(template) do
    available_functions = Functions.list_function_names()
    used_functions = extract_function_calls(template)

    unknown_functions =
      used_functions
      |> Enum.reject(&(&1 in available_functions))

    if Enum.empty?(unknown_functions) do
      {:ok,
       %{
         type: :functions,
         status: :valid,
         message: "All functions are available",
         used_functions: used_functions
       }}
    else
      {:error,
       %{
         type: :functions,
         status: :error,
         message: "Unknown functions detected",
         unknown_functions: unknown_functions,
         available_functions: available_functions
       }}
    end
  end

  @doc """
  Extracts all variable references from a template.

  ## Parameters
  - `template` - The template string to analyze

  ## Returns
  - List of variable names used in the template
  """
  @spec extract_variables(String.t()) :: [String.t()]
  def extract_variables(template) do
    # Extract {{ variable }} patterns
    variable_pattern = ~r/\{\{\s*([a-zA-Z_][a-zA-Z0-9_\.]*)\s*\}\}/

    Regex.scan(variable_pattern, template, capture: :all_but_first)
    |> Enum.map(&List.first/1)
    |> Enum.uniq()
    |> Enum.reject(&is_function_call?/1)
  end

  @doc """
  Extracts all function calls from a template.

  ## Parameters
  - `template` - The template string to analyze

  ## Returns
  - List of function names called in the template
  """
  @spec extract_function_calls(String.t()) :: [String.t()]
  def extract_function_calls(template) do
    # Extract function calls like {{ function_name() }} or {{ function_name(args) }}
    function_pattern = ~r/\{\{\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/

    Regex.scan(function_pattern, template, capture: :all_but_first)
    |> Enum.map(&List.first/1)
    |> Enum.uniq()
  end

  # Private helper functions

  defp parse_syntax_error(reason) when is_binary(reason) do
    # Try to extract line and column information from error message
    case Regex.run(~r/line (\d+)/, reason) do
      [_, line] -> %{line: String.to_integer(line)}
      _ -> %{}
    end
  end

  defp parse_syntax_error(_), do: %{}

  defp check_file_access(template) do
    dangerous_patterns = [
      ~r/file\s*\(/i,
      ~r/read\s*\(/i,
      ~r/write\s*\(/i,
      ~r/include\s*\(/i
    ]

    Enum.flat_map(dangerous_patterns, fn pattern ->
      if Regex.match?(pattern, template) do
        [
          %{
            type: :file_access,
            severity: :high,
            message: "Potential file access detected"
          }
        ]
      else
        []
      end
    end)
  end

  defp check_system_calls(template) do
    dangerous_patterns = [
      ~r/system\s*\(/i,
      ~r/exec\s*\(/i,
      ~r/eval\s*\(/i,
      ~r/shell\s*\(/i
    ]

    Enum.flat_map(dangerous_patterns, fn pattern ->
      if Regex.match?(pattern, template) do
        [
          %{
            type: :system_call,
            severity: :critical,
            message: "Potential system call detected"
          }
        ]
      else
        []
      end
    end)
  end

  defp check_infinite_loops(template) do
    # Check for potentially infinite loops
    loop_patterns = [
      # for i in (1..)
      ~r/\{\%\s*for\s+\w+\s+in\s+\(\d+\.\.\)/,
      ~r/\{\%\s*while\s+true/i
    ]

    Enum.flat_map(loop_patterns, fn pattern ->
      if Regex.match?(pattern, template) do
        [
          %{
            type: :infinite_loop,
            severity: :high,
            message: "Potential infinite loop detected"
          }
        ]
      else
        []
      end
    end)
  end

  defp check_excessive_nesting(template) do
    # Count nesting levels
    max_nesting = count_max_nesting(template)

    if max_nesting > 5 do
      [
        %{
          type: :excessive_nesting,
          severity: :medium,
          message: "Excessive nesting detected (#{max_nesting} levels)",
          max_nesting: max_nesting
        }
      ]
    else
      []
    end
  end

  defp count_max_nesting(template) do
    # Simple nesting counter for {% %} blocks
    template
    |> String.graphemes()
    |> Enum.reduce({0, 0}, fn
      "{", {current, max} -> {current + 1, Kernel.max(current + 1, max)}
      "}", {current, max} -> {Kernel.max(current - 1, 0), max}
      _, acc -> acc
    end)
    |> elem(1)
  end

  defp check_template_complexity(template) do
    # Count various complexity indicators
    line_count = String.split(template, "\n") |> length()
    variable_count = extract_variables(template) |> length()
    function_count = extract_function_calls(template) |> length()

    warnings = []

    warnings =
      if line_count > 50 do
        [
          %{
            type: :template_size,
            severity: :low,
            message: "Template is quite large (#{line_count} lines)"
          }
          | warnings
        ]
      else
        warnings
      end

    warnings =
      if variable_count > 20 do
        [
          %{
            type: :variable_count,
            severity: :low,
            message: "Many variables used (#{variable_count})"
          }
          | warnings
        ]
      else
        warnings
      end

    warnings =
      if function_count > 15 do
        [
          %{
            type: :function_count,
            severity: :low,
            message: "Many functions used (#{function_count})"
          }
          | warnings
        ]
      else
        warnings
      end

    warnings
  end

  defp check_loop_complexity(template) do
    # Count nested loops
    loop_count = Regex.scan(~r/\{\%\s*for\s/, template) |> length()

    if loop_count > 3 do
      [
        %{
          type: :loop_complexity,
          severity: :medium,
          message: "Multiple loops detected (#{loop_count})"
        }
      ]
    else
      []
    end
  end

  defp check_function_usage(template) do
    # Check for potentially expensive functions
    expensive_functions = ["hash"]

    used_expensive =
      extract_function_calls(template)
      |> Enum.filter(&(&1 in expensive_functions))

    if length(used_expensive) > 0 do
      [
        %{
          type: :expensive_functions,
          severity: :low,
          message: "Potentially expensive functions used: #{Enum.join(used_expensive, ", ")}"
        }
      ]
    else
      []
    end
  end

  defp is_function_call?(variable) do
    String.contains?(variable, "(") ||
      variable in Functions.list_function_names()
  end

  defp extract_warnings(validation_results) do
    validation_results
    |> Enum.flat_map(fn result ->
      Map.get(result, :warnings, [])
    end)
  end
end
