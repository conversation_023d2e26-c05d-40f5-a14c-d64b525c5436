defmodule Mqttable.MqttClient.ErrorHandler do
  @moduledoc """
  Centralized error handling and message formatting for MQTT client operations.

  This module provides pure functions for error classification, message formatting,
  and error recovery strategies. All functions are side-effect free and focus on
  transforming error data into user-friendly messages.
  """

  def format_mqtt_error(reason) do
    case reason do
      # Connection errors
      :econnrefused ->
        "Connection refused: The broker is not reachable or not running"

      :timeout ->
        "Connection timeout: The broker did not respond in time"

      :nxdomain ->
        "Host not found: The broker hostname could not be resolved"

      :econnreset ->
        "Connection reset: The connection was forcibly closed by the broker"

      :closed ->
        "Connection closed: The connection was closed unexpectedly"

      :etimedout ->
        "Connection timed out: The operation timed out"

      :ehostunreach ->
        "Host unreachable: The broker host is unreachable"

      :enetunreach ->
        "Network unreachable: The network is unreachable"

      :tcp_closed ->
        "Connection closed: The broker closed the connection before completing the handshake"

      {:shutdown, :tcp_closed} ->
        "Connection closed: The broker closed the connection before completing the handshake"

      {:socket_closed_before_connack, _} ->
        "Connection closed: The broker closed the connection before completing the handshake"

      # Authentication errors
      :not_authorized ->
        "Not authorized: Authentication failed, check your username and password"

      :bad_username_or_password ->
        "Bad username or password: Authentication failed, check your credentials"

      :eacces ->
        "Permission denied: Access to the broker was denied"

      # Protocol errors
      :protocol_error ->
        "Protocol error: There was an error in the MQTT protocol"

      :einval ->
        "Invalid argument: One of the connection parameters is invalid"

      :badarg ->
        "Bad argument: One of the connection parameters is invalid"

      :already_present ->
        "Client ID already in use: Another client with the same ID is already connected"

      # Network/SSL errors
      :unknown_ca ->
        "Unknown CA: The server's certificate authority is unknown"

      :cert_verify_failed ->
        "Certificate verification failed: The server's certificate could not be verified"

      # Default case
      _ ->
        "Connection error: #{inspect(reason)}"
    end
  end

  def extract_concise_error_message(reason) do
    case reason do
      {:shutdown, :tcp_closed} ->
        "Connection closed by broker before completing handshake"

      {:socket_closed_before_connack, _} ->
        "Connection closed by broker before completing handshake"

      {:badmatch, {:error, error_reason}} when is_atom(error_reason) ->
        format_mqtt_error(error_reason)

      reason when is_atom(reason) ->
        format_mqtt_error(reason)

      _ ->
        "Unexpected error: #{inspect(reason)}"
    end
  end
end
