defmodule Mqttable.MqttClient.Manager do
  @moduledoc """
  DynamicSupervisor for MQTT client worker processes.
  This module manages individual worker processes for each MQTT client connection.
  """
  use DynamicSupervisor
  require Logger

  alias Mqttable.MqttClient.{Worker, ClientRegistry}

  @status_disconnected :disconnected

  def start_link(opts \\ []) do
    DynamicSupervisor.start_link(__MODULE__, opts, name: __MODULE__)
  end

  def establish_client_connection(connection, broker) do
    connection
    |> extract_client_info(broker)
    |> determine_connection_strategy()
  end

  def terminate_client_connection(broker_name, client_id) do
    {broker_name, client_id}
    |> fetch_client_info()
    |> execute_disconnection_strategy()
  end

  def add_topic_subscription(broker_name, client_id, topic, opts) do
    Worker.subscribe(broker_name, client_id, topic, opts)
  end

  def remove_topic_subscription(broker_name, client_id, topic) do
    Worker.unsubscribe(broker_name, client_id, topic)
  end

  def send_message_to_topic(broker_name, client_id, topic, payload, opts) do
    Worker.publish(broker_name, client_id, topic, payload, opts)
  end

  def retrieve_client_status(broker_name, client_id) do
    ClientRegistry.get_client_status(broker_name, client_id)
  end

  def list_all_connected_clients do
    ClientRegistry.list_connected_clients()
  end

  def restore_all_saved_connections(connection_sets) do
    connection_sets
    |> prepare_connection_data()
    |> activate_all_connections()
    |> handle_activation_results()
  end

  @impl true
  @spec init(keyword()) :: {:ok, DynamicSupervisor.sup_flags()}
  def init(_opts) do
    :ets.new(__MODULE__, [:set, :public, :named_table])
    DynamicSupervisor.init(strategy: :one_for_one)
  end

  def retrieve_client_parse_state(broker_name, client_id) do
    ClientRegistry.get_client_parse_state(broker_name, client_id)
  end

  def persist_client_parse_state(broker_name, client_id, parse_state, data_size) do
    ClientRegistry.update_client_parse_state(broker_name, client_id, parse_state, data_size)
  end

  # Private helper functions for functional composition

  defp extract_client_info(connection, broker) do
    %{
      client_id: connection.client_id,
      broker_name: broker.name,
      connection: connection,
      broker: broker,
      current_status: ClientRegistry.get_client_status(broker.name, connection.client_id)
    }
  end

  defp determine_connection_strategy(%{current_status: @status_disconnected} = info) do
    launch_new_worker(info.connection, info.broker)
  end

  defp determine_connection_strategy(info) do
    info
    |> fetch_existing_worker_args()
    |> resolve_worker_conflict()
  end

  defp fetch_client_info({broker_name, client_id}) do
    %{
      broker_name: broker_name,
      client_id: client_id,
      client_info: ClientRegistry.get_client_info(broker_name, client_id)
    }
  end

  defp execute_disconnection_strategy(%{client_info: {@status_disconnected, _, _, _}}) do
    :ok
  end

  defp execute_disconnection_strategy(%{
         client_info: {_, worker_pid, _, _},
         broker_name: broker_name,
         client_id: client_id
       })
       when worker_pid != nil do
    DynamicSupervisor.terminate_child(__MODULE__, worker_pid)
    ClientRegistry.unregister_client(broker_name, client_id)
    :ok
  end

  defp execute_disconnection_strategy(%{
         client_info: {_, nil, _, _},
         broker_name: broker_name,
         client_id: client_id
       }) do
    ClientRegistry.unregister_client(broker_name, client_id)
    :ok
  end

  defp fetch_existing_worker_args(info) do
    case Worker.retrieve_startup_args(info.broker_name, info.client_id) do
      {:ok, current_args} ->
        new_args = {Map.delete(info.broker, :connections), info.connection}
        Map.merge(info, %{current_args: current_args, new_args: new_args, worker_found: true})

      {:error, :not_found} ->
        Map.put(info, :worker_found, false)
    end
  end

  defp resolve_worker_conflict(%{worker_found: false} = info) do
    launch_new_worker(info.connection, info.broker)
  end

  defp resolve_worker_conflict(%{current_args: args, new_args: args} = info) do
    {:ok, info.client_id}
  end

  defp resolve_worker_conflict(info) do
    Logger.debug(
      "Restarting worker for client #{info.broker.name} #{info.client_id} due to changed startup arguments"
    )

    :ok = terminate_client_connection(info.broker.name, info.client_id)
    launch_new_worker(info.connection, info.broker)
  end

  defp launch_new_worker(connection, broker) do
    client_id = connection.client_id
    args = {Map.delete(broker, :connections), connection}

    {DynamicSupervisor.start_child(__MODULE__, {Worker, args}), client_id}
    |> handle_worker_launch_result()
  end

  defp handle_worker_launch_result({{:ok, _worker_pid}, client_id}) do
    {:ok, client_id}
  end

  defp handle_worker_launch_result({{:error, {:already_started, _worker_pid}}, client_id}) do
    {:ok, client_id}
  end

  defp handle_worker_launch_result({{:error, reason}, _client_id}) do
    {:error, reason, "Failed to start worker process"}
  end

  defp prepare_connection_data(connection_sets) do
    Enum.flat_map(connection_sets, fn connection_set ->
      broker = Map.delete(connection_set, :connections)
      connections = connection_set[:connections] || []

      Enum.map(connections, fn connection ->
        %{broker: broker, connection: connection, client_id: connection.client_id}
      end)
    end)
  end

  defp activate_all_connections(connection_data) do
    Enum.map(connection_data, &attempt_connection_activation/1)
  end

  defp attempt_connection_activation(%{broker: broker, client_id: client_id} = data) do
    ClientRegistry.get_client_status(broker.name, client_id)
    |> handle_connection_activation(data)
  end

  defp handle_connection_activation(@status_disconnected, %{
         broker: broker,
         connection: connection,
         client_id: client_id
       }) do
    args = {broker, connection}

    DynamicSupervisor.start_child(__MODULE__, {Worker, args})
    |> log_activation_result(client_id)
  end

  defp handle_connection_activation(_status, %{client_id: client_id}) do
    Logger.debug("Worker already exists for saved connection: #{client_id}")
    {:ok, :already_exists}
  end

  defp log_activation_result({:ok, _worker_pid}, client_id) do
    Logger.debug("Started worker for saved connection: #{client_id}")
    {:ok, :started}
  end

  defp log_activation_result({:error, {:already_started, _worker_pid}}, client_id) do
    Logger.debug("Worker already exists for client: #{client_id}")
    {:ok, :already_exists}
  end

  defp log_activation_result({:error, reason}, client_id) do
    Logger.warning("Failed to start worker for saved connection #{client_id}: #{inspect(reason)}")
    {:error, reason}
  end

  defp handle_activation_results(results) do
    errors = Enum.filter(results, &match?({:error, _}, &1))

    if Enum.empty?(errors) do
      :ok
    else
      Logger.warning("Some connections failed to start: #{length(errors)} errors")
      :ok
    end
  end
end
