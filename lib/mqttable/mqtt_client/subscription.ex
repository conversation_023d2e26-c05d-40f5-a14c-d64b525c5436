defmodule Mqttable.MqttClient.Subscription do
  @moduledoc """
  Pure functions for managing MQTT topic subscriptions and resubscriptions.

  This module provides composable functions for handling topic subscriptions,
  validation, and resubscription logic during client reconnections. All functions
  are pure and side-effect free, focusing on data transformation and validation.
  """

  require Logger
  alias Mqttable.MqttClient.Topic

  def validate_topic_filter(topic) do
    try do
      Topic.validate({:filter, topic})
      :ok
    rescue
      e in RuntimeError ->
        {:error, "Invalid topic filter: #{e.message}"}
    catch
      :error, reason ->
        {:error, "Invalid topic filter: #{inspect(reason)}"}
    end
  end

  def to_subscription_options(options) do
    Enum.map(options, fn
      {key, value} when key in [:nl, :rap] -> {key, convert_boolean_flag(value)}
      option -> option
    end)
  end

  def prepare_subscription_properties(sub_id) when is_integer(sub_id) and sub_id > 0,
    do: %{:"Subscription-Identifier" => sub_id}

  def prepare_subscription_properties(_), do: %{}

  def check_subscription_success(reason_codes) do
    reason_codes
    |> Enum.find(&subscription_has_error?/1)
    |> evaluate_subscription_result()
  end

  def build_subscription_options(topic_map) do
    sub_opts = create_subscription_options_list(topic_map)
    props = extract_subscription_properties(topic_map)
    {sub_opts, props}
  end

  def process_saved_topics_for_resubscription(topics) do
    topics
    |> filter_valid_topic_entries()
    |> extract_topics_and_options()
  end

  def prepare_topic_for_subscription(topic, sub_opts, props) do
    case validate_topic_filter(topic) do
      :ok -> {:ok, {topic, to_subscription_options(sub_opts), props}}
      {:error, _} = error -> error
    end
  end

  # Private helper functions

  defp convert_boolean_flag(0), do: false
  defp convert_boolean_flag(1), do: true
  defp convert_boolean_flag(value), do: value

  defp subscription_has_error?(code) when code in [0, 1, 2], do: false
  defp subscription_has_error?(_), do: true

  defp evaluate_subscription_result(nil), do: :ok

  defp evaluate_subscription_result(error_code),
    do: {:error, format_subscription_error(error_code)}

  defp format_subscription_error(0x80), do: "Subscription failed: Unspecified error"
  defp format_subscription_error(0x83), do: "Implementation specific error"
  defp format_subscription_error(0x87), do: "Not authorized"
  defp format_subscription_error(0x8F), do: "Topic filter invalid"
  defp format_subscription_error(0x91), do: "Packet identifier in use"
  defp format_subscription_error(0x97), do: "Quota exceeded"
  defp format_subscription_error(0x9E), do: "Shared subscriptions not supported"
  defp format_subscription_error(0xA1), do: "Subscription identifiers not supported"
  defp format_subscription_error(0xA2), do: "Wildcard subscriptions not supported"
  defp format_subscription_error(code), do: "Subscription failed with reason code: #{code}"

  defp create_subscription_options_list(topic_map) do
    [{:qos, Map.get(topic_map, :qos, 0)}]
    |> maybe_add_option(topic_map, :nl)
    |> maybe_add_option(topic_map, :rap)
    |> maybe_add_option(topic_map, :rh)
    |> to_subscription_options()
  end

  defp extract_subscription_properties(topic_map) do
    topic_map
    |> Map.get(:id)
    |> prepare_subscription_properties()
  end

  defp maybe_add_option(opts, map, key) do
    maybe_add_option_value(opts, map, key, Map.get(map, key))
  end

  defp maybe_add_option_value(opts, _map, _key, nil), do: opts
  defp maybe_add_option_value(opts, _map, key, value), do: [{key, value} | opts]

  defp filter_valid_topic_entries(topics) do
    Enum.filter(topics, &is_valid_topic_entry?/1)
  end

  defp extract_topics_and_options(topics) do
    Enum.map(topics, &extract_topic_and_options/1)
  end

  defp is_valid_topic_entry?(nil), do: false
  defp is_valid_topic_entry?(%{topic: topic}) when is_binary(topic) and topic != "", do: true
  defp is_valid_topic_entry?(_), do: false

  defp extract_topic_and_options(%{topic: topic} = topic_map) do
    {sub_opts, props} = build_subscription_options(topic_map)
    {topic, {sub_opts, props}}
  end

  def log_resubscription_attempt(client_id, topic, opts, :ok),
    do:
      Logger.info(
        "Successfully resubscribed client #{client_id} to topic #{topic} with opts #{inspect(opts)}"
      )

  def log_resubscription_attempt(client_id, topic, opts, :error),
    do:
      Logger.error(
        "Failed to resubscribe client #{client_id} to topic #{topic} with opts #{inspect(opts)}"
      )

  def log_topic_validation_failure(topic, error_message) do
    Logger.error("Topic validation failed for resubscription to #{topic}: #{error_message}")
  end
end
