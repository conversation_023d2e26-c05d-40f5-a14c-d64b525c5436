defmodule Mqttable.MqttClient.ConnectionBuilder do
  @moduledoc """
  Pure functions for building MQTT connection options.

  This module provides composable functions for constructing MQTT client
  configuration options from connection and broker parameters. All functions
  are pure and side-effect free, making them easily testable and composable.
  """

  alias MqttableWeb.Utils.ConnectionHelpers

  # Type definitions
  @type connection :: map()
  @type broker :: map()
  @type mqtt_opts :: keyword()
  @type mqtt_properties :: map()

  def build_mqtt_options(connection, broker) do
    connection
    |> ConnectionHelpers.prepare_connection_for_mqtt()
    |> build_base_options(broker)
    |> add_authentication_options(connection)
    |> add_protocol_options(broker)
    |> add_mqtt_version_options(connection)
  end

  defp build_base_options(connection, broker) do
    [
      clientid: connection.client_id,
      clean_start: connection.clean_start,
      keepalive: connection.keep_alive,
      broker_name: broker.name,
      proto_ver: mqtt_version_to_proto_ver(connection.mqtt_version)
    ]
  end

  defp add_authentication_options(opts, connection) do
    opts
    |> add_username(connection)
    |> add_password(connection)
    |> add_will_message(connection)
  end

  defp add_protocol_options(opts, broker) do
    opts
    |> add_host_and_port(broker)
    |> add_transport_options(broker)
  end

  defp add_mqtt_version_options(opts, connection = %{mqtt_version: "5.0"}),
    do: add_mqtt5_properties(opts, connection)

  defp add_mqtt_version_options(opts, _), do: opts

  defp mqtt_version_to_proto_ver("5.0"), do: :v5
  defp mqtt_version_to_proto_ver("3.1.1"), do: :v4
  defp mqtt_version_to_proto_ver("3.1"), do: :v3
  defp mqtt_version_to_proto_ver(_), do: :v4

  defp add_host_and_port(opts, broker) do
    [
      {:host, String.to_charlist(broker.host)},
      {:port, String.to_integer(broker.port)}
      | opts
    ]
  end

  defp add_transport_options(opts, broker = %{protocol: "mqtts"}) do
    ssl_opts = build_ssl_options(broker)
    [{:ssl, true}, {:ssl_opts, ssl_opts} | opts]
  end

  defp add_transport_options(opts, broker = %{protocol: "wss"}) do
    ws_path = Map.get(broker, :ws_path, "/mqtt") |> String.to_charlist()
    ssl_opts = build_ssl_options(broker)
    # For WSS, SSL options need to be passed via ws_transport_options to Gun library
    # Force HTTP/1.1 protocol as some servers don't support WebSocket over HTTP/2
    ws_transport_opts = [
      {:transport, :tls},
      {:tls_opts, ssl_opts},
      # Force HTTP/1.1
      {:protocols, [:http]}
    ]

    # Try without specifying subprotocol - let server decide
    # Some servers are picky about WebSocket subprotocol negotiation
    ws_headers = []

    [
      {:ws_path, ws_path},
      {:ws_transport_options, ws_transport_opts},
      {:ws_headers, ws_headers}
      | opts
    ]
  end

  defp add_transport_options(opts, broker = %{protocol: "ws"}) do
    ws_path = Map.get(broker, :ws_path, "/mqtt") |> String.to_charlist()
    # Try without specifying subprotocol for ws connections too
    ws_headers = []

    [{:ws_path, ws_path}, {:ws_headers, ws_headers} | opts]
  end

  defp add_transport_options(opts, _), do: opts

  defp build_ssl_options(broker) do
    []
    |> add_ssl_verification(broker)
    |> add_ca_certificate(broker)
    |> add_client_certificates(broker)
    |> add_alpn_protocol(broker)
  end

  defp add_ssl_verification(ssl_opts, %{ssl_enabled: false}) do
    # SSL verification disabled, skip all certificate validation
    [{:verify, :verify_none} | ssl_opts]
  end

  defp add_ssl_verification(ssl_opts, broker = %{ssl_enabled: true}) do
    # For CA-signed certificates, enable peer verification
    if Map.get(broker, :ca_file, "") != "" do
      [{:verify, :verify_peer} | ssl_opts]
    else
      # No CA file provided, disable verification
      [{:verify, :verify_none} | ssl_opts]
    end
  end

  defp add_ca_certificate(ssl_opts, %{ca_file: ca_file}) when ca_file != "" do
    ca_file_path = resolve_certificate_path(ca_file)
    [{:cacertfile, ca_file_path} | ssl_opts]
  end

  defp add_ca_certificate(ssl_opts, _broker), do: ssl_opts

  defp add_client_certificates(ssl_opts, broker) do
    ssl_opts
    |> add_client_certificate(:client_cert_file, :certfile, broker)
    |> add_client_certificate(:client_key_file, :keyfile, broker)
  end

  defp add_client_certificate(ssl_opts, from_key, to_key, broker) do
    from_file = Map.get(broker, from_key, "")

    if from_file != "" do
      file_path = resolve_certificate_path(from_file)
      [{to_key, file_path} | ssl_opts]
    else
      ssl_opts
    end
  end

  defp add_alpn_protocol(ssl_opts, %{alpn: alpn}) when alpn != "" do
    [{:alpn_advertised_protocols, [String.to_charlist(alpn)]} | ssl_opts]
  end

  defp add_alpn_protocol(ssl_opts, _broker), do: ssl_opts

  defp resolve_certificate_path(path) when is_binary(path) do
    if String.starts_with?(path, "/certificates/") do
      # Remove the leading "/certificates/" and build absolute path
      filename = String.replace_prefix(path, "/certificates/", "")
      Path.join([:code.priv_dir(:mqttable), "data", "certificates", filename])
    else
      # Assume it's already an absolute path or relative to current directory
      path
    end
    |> String.to_charlist()
  end

  defp add_username(opts, %{username: nil}), do: opts
  defp add_username(opts, %{username: ""}), do: opts
  defp add_username(opts, %{username: username}), do: [{:username, username} | opts]
  defp add_username(opts, _), do: opts

  defp add_password(opts, %{password: nil}), do: opts
  defp add_password(opts, %{password: ""}), do: opts
  defp add_password(opts, %{password: password}), do: [{:password, password} | opts]
  defp add_password(opts, _), do: opts

  defp add_will_message(opts, %{will_topic: nil}), do: opts
  defp add_will_message(opts, %{will_topic: ""}), do: opts

  defp add_will_message(opts, %{will_topic: _} = connection),
    do: build_will_options(opts, connection)

  defp add_will_message(opts, _), do: opts

  defp build_will_options(opts, connection) do
    will_opts = [
      will_topic: connection.will_topic,
      will_payload: connection.will_payload || "",
      will_qos: String.to_integer(connection.will_qos || "0"),
      will_retain: connection.will_retain
    ]

    opts = Keyword.merge(opts, will_opts)
    add_mqtt5_will_properties(opts, connection)
  end

  defp add_mqtt5_will_properties(opts, connection = %{mqtt_version: "5.0"}) do
    will_props = build_mqtt5_will_properties(connection)

    if will_props != %{} do
      [{:will_props, will_props} | opts]
    else
      opts
    end
  end

  defp add_mqtt5_will_properties(opts, _), do: opts

  defp add_mqtt5_properties(opts, connection) do
    properties =
      %{}
      |> add_session_expiry_interval(connection)
      |> add_receive_maximum(connection)
      |> add_maximum_packet_size(connection)
      |> add_topic_alias_maximum(connection)
      |> add_request_response_info(connection)
      |> add_request_problem_info(connection)
      |> add_user_properties(connection)

    if properties != %{} do
      [{:properties, properties} | opts]
    else
      opts
    end
  end

  def build_mqtt5_will_properties(connection) do
    %{}
    |> add_will_delay_interval(connection)
    |> add_payload_format_indicator(connection)
    |> add_message_expiry_interval(connection)
    |> add_content_type(connection)
    |> add_response_topic(connection)
    |> add_correlation_data(connection)
  end

  # MQTT 5.0 connection properties helpers
  defp add_session_expiry_interval(props, connection) do
    if is_integer(connection.session_expiry_interval) && connection.session_expiry_interval > 0 do
      Map.put(props, :"Session-Expiry-Interval", connection.session_expiry_interval)
    else
      props
    end
  end

  defp add_receive_maximum(props, connection) do
    if is_integer(connection.receive_maximum) && connection.receive_maximum > 0 do
      Map.put(props, :"Receive-Maximum", connection.receive_maximum)
    else
      props
    end
  end

  defp add_maximum_packet_size(props, connection) do
    if is_integer(connection.maximum_packet_size) && connection.maximum_packet_size > 0 do
      Map.put(props, :"Maximum-Packet-Size", connection.maximum_packet_size)
    else
      props
    end
  end

  defp add_topic_alias_maximum(props, connection) do
    if is_integer(connection.topic_alias_maximum) && connection.topic_alias_maximum > 0 do
      Map.put(props, :"Topic-Alias-Maximum", connection.topic_alias_maximum)
    else
      props
    end
  end

  defp add_request_response_info(props, connection) do
    if connection.request_response_info do
      Map.put(props, :"Request-Response-Information", 1)
    else
      props
    end
  end

  defp add_request_problem_info(props, connection) do
    if connection.request_problem_info do
      Map.put(props, :"Request-Problem-Information", 1)
    else
      props
    end
  end

  defp add_user_properties(props, connection) do
    if connection.user_properties && is_list(connection.user_properties) &&
         length(connection.user_properties) > 0 do
      valid_props =
        connection.user_properties
        |> Enum.filter(&valid_user_property?/1)
        |> Enum.map(fn prop -> {prop.key, prop.value} end)

      if valid_props != [] do
        Map.put(props, :"User-Property", valid_props)
      else
        props
      end
    else
      props
    end
  end

  defp valid_user_property?(prop) do
    is_map(prop) && Map.has_key?(prop, :key) && Map.has_key?(prop, :value) &&
      prop.key != "" && prop.value != ""
  end

  # MQTT 5.0 will properties helpers

  defp add_will_delay_interval(props, connection) do
    if is_integer(connection.will_delay_interval) && connection.will_delay_interval > 0 do
      Map.put(props, :"Will-Delay-Interval", connection.will_delay_interval)
    else
      props
    end
  end

  defp add_payload_format_indicator(props, connection) do
    if connection.will_payload_format do
      Map.put(props, :"Payload-Format-Indicator", 1)
    else
      props
    end
  end

  defp add_message_expiry_interval(props, connection) do
    if is_integer(connection.will_message_expiry) && connection.will_message_expiry > 0 do
      Map.put(props, :"Message-Expiry-Interval", connection.will_message_expiry)
    else
      props
    end
  end

  defp add_content_type(props, connection) do
    if connection.will_content_type && connection.will_content_type != "" do
      Map.put(props, :"Content-Type", connection.will_content_type)
    else
      props
    end
  end

  defp add_response_topic(props, connection) do
    if connection.will_response_topic && connection.will_response_topic != "" do
      Map.put(props, :"Response-Topic", connection.will_response_topic)
    else
      props
    end
  end

  defp add_correlation_data(props, connection) do
    if connection.will_correlation_data && connection.will_correlation_data != "" do
      Map.put(props, :"Correlation-Data", connection.will_correlation_data)
    else
      props
    end
  end
end
