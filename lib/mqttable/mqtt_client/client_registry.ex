defmodule Mqttable.MqttClient.ClientRegistry do
  @moduledoc """
  MQTT client registry for managing client state and metadata.

  This module provides a clean interface for all ETS table operations
  related to MQTT client state management. It encapsulates the ETS table structure
  and provides type-safe operations for client lifecycle management.
  """
  @table Mqttable.MqttClient.Manager

  def register_client(broker_name, client_id, worker_pid, client_pid, mqtt_opts, status) do
    opts = mqtt_opts || []
    parse_state = build_parse_state(opts)
    key = {broker_name, client_id}
    data_size = 0
    record = {key, worker_pid, client_pid, opts, parse_state, status, data_size}
    :ets.insert(@table, record)
  end

  def unregister_client(broker_name, client_id) do
    :ets.delete(@table, {broker_name, client_id})
  end

  def get_client_status(broker_name, client_id) do
    case :ets.lookup(@table, {broker_name, client_id}) do
      [{{_, _}, _, _, _, _, status, _}] ->
        status

      [] ->
        :disconnected
    end
  end

  def get_client_parse_state(broker_name, client_id) do
    case :ets.lookup(@table, {broker_name, client_id}) do
      [{{_, _}, _, _, _, parse_state, _status, data_size}] ->
        {parse_state, data_size}

      [] ->
        nil
    end
  end

  def update_client_parse_state(broker_name, client_id, parse_state, data_size) do
    :ets.update_element(@table, {broker_name, client_id}, [{5, parse_state}, {7, data_size}])
  end

  def get_client_info(broker_name, client_id) do
    case :ets.lookup(@table, {broker_name, client_id}) do
      [{{_, _}, worker_pid, client_pid, mqtt_opts, _, status, _data_size}] ->
        {status, worker_pid, client_pid, mqtt_opts}

      [] ->
        {:disconnected, nil, nil, nil}
    end
  end

  def list_connected_clients do
    :ets.tab2list(@table)
    |> Enum.filter(fn {_, _, _, _, _, status, _} ->
      status == :connected
    end)
    |> Enum.map(fn {{broker_name, client_id}, _, _, mqtt_opts, _, status, _} ->
      %{
        broker_name: broker_name,
        client_id: client_id,
        status: status,
        mqtt_version: extract_mqtt_version_from_opts(mqtt_opts)
      }
    end)
  end

  defp build_parse_state(opts) do
    max_size =
      opts
      |> Keyword.get(:properties, %{})
      |> Map.get(:"Receive-Maximum", 0xFFFFFFF)

    version =
      case Keyword.get(opts, :proto_ver, :v5) do
        :v5 -> 5
        _ -> 4
      end

    :emqtt_frame.initial_parse_state(%{max_size: max_size, version: version})
  end

  defp extract_mqtt_version_from_opts(opts) when is_list(opts) do
    case Keyword.get(opts, :proto_ver, :v5) do
      :v5 -> "5.0"
      :v4 -> "3.1.1"
      :v3 -> "3.1"
      _ -> "5.0"
    end
  end

  defp extract_mqtt_version_from_opts(_), do: "5.0"
end
