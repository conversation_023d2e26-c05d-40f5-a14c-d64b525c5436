defmodule Mqttable.Encryption do
  @moduledoc """
  Module for handling encryption and decryption of sensitive data.
  Provides functions to securely encrypt and decrypt passwords and other sensitive information.
  """

  require Logger

  # AES block size in bytes
  @block_size 16
  # Prefix for encrypted data to identify it
  @encryption_prefix "encrypted:"

  @doc """
  Encrypts a string using AES-256-CBC with a random IV.
  Returns the encrypted string with a prefix to identify it as encrypted.

  ## Parameters
  - `data`: The string to encrypt

  ## Returns
  - Encrypted string with prefix if encryption succeeds
  - Original string if encryption fails or data is nil/empty
  """
  def encrypt(data) when is_binary(data) and byte_size(data) > 0 do
    try do
      # Get the encryption key
      key = get_encryption_key()

      # Generate a random IV
      iv = :crypto.strong_rand_bytes(@block_size)

      # Pad the data to match the block size
      padded_data = pad(data, @block_size)

      # Encrypt the data
      encrypted = :crypto.crypto_one_time(:aes_256_cbc, key, iv, padded_data, true)

      # Combine IV and encrypted data, then encode to Base64
      combined = iv <> encrypted
      encoded = Base.encode64(combined)

      # Add prefix to identify as encrypted
      @encryption_prefix <> encoded
    rescue
      e ->
        Logger.error("Encryption failed: #{inspect(e)}")
        # Return original data if encryption fails
        data
    end
  end

  # Return original data if it's nil or empty
  def encrypt(data) when is_nil(data) or data == "", do: ""
  def encrypt(data), do: data

  @doc """
  Decrypts a string that was encrypted with encrypt/1.

  ## Parameters
  - `data`: The encrypted string to decrypt

  ## Returns
  - Decrypted string if decryption succeeds
  - Original string if data is not encrypted or decryption fails
  """
  def decrypt(@encryption_prefix <> encoded_data) do
    try do
      # Get the encryption key
      key = get_encryption_key()

      # Decode the Base64 data
      combined = Base.decode64!(encoded_data)

      # Extract IV and encrypted data
      <<iv::binary-size(@block_size), encrypted::binary>> = combined

      # Decrypt the data
      decrypted = :crypto.crypto_one_time(:aes_256_cbc, key, iv, encrypted, false)

      # Remove padding
      unpad(decrypted)
    rescue
      e ->
        Logger.error("Decryption failed: #{inspect(e)}")
        # Return original data if decryption fails
        @encryption_prefix <> encoded_data
    end
  end

  # Return original data if it's not encrypted
  def decrypt(data), do: data

  @doc """
  Checks if a string is encrypted.

  ## Parameters
  - `data`: The string to check

  ## Returns
  - `true` if the string is encrypted
  - `false` otherwise
  """
  def encrypted?(data) when is_binary(data) do
    String.starts_with?(data, @encryption_prefix)
  end

  def encrypted?(_), do: false

  # Private function to get the encryption key
  defp get_encryption_key do
    # Use the Phoenix secret key base as the encryption key
    # This ensures the key is consistent across application restarts
    secret_key_base = Application.get_env(:mqttable, MqttableWeb.Endpoint)[:secret_key_base]

    if is_nil(secret_key_base) do
      # If secret_key_base is not available, generate a key from a default value
      # This is less secure but allows the application to function
      Logger.warning("No secret_key_base found, using default key (less secure)")
      :crypto.hash(:sha256, "mqttable_default_encryption_key")
    else
      # Use the first 32 bytes of the SHA-256 hash of the secret key base
      :crypto.hash(:sha256, secret_key_base)
    end
  end

  # PKCS#7 padding implementation
  defp pad(data, block_size) do
    # Calculate padding length
    padding_length = block_size - rem(byte_size(data), block_size)
    # Create padding bytes
    padding = :binary.copy(<<padding_length>>, padding_length)
    # Append padding to data
    data <> padding
  end

  # PKCS#7 unpadding implementation
  defp unpad(data) do
    # Get the last byte which indicates padding length
    padding_length = :binary.last(data)
    # Remove padding
    binary_part(data, 0, byte_size(data) - padding_length)
  end
end
