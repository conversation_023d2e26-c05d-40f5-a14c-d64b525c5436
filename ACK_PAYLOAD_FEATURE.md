# ACK消息Payload显示和失败高亮功能

## 功能概述

实现了两个主要功能：

1. **ACK消息Payload显示**: 对于没有payload的ACK消息，将reason code信息显示在payload列中
2. **失败ACK高亮**: 对于返回错误的ACK消息，整行高亮显示表示收到了失败的ACK

## 实现的ACK消息类型

支持以下MQTT ACK消息类型：
- `PUBACK` - Publish Acknowledgment
- `PUBREC` - Publish Received
- `PUBREL` - Publish Release  
- `PUBCOMP` - Publish Complete
- `SUBACK` - Subscribe Acknowledgment
- `UNSUBACK` - Unsubscribe Acknowledgment
- `CONNACK` - Connect Acknowledgment
- `DISCONNECT` - Disconnect

## 后端实现 (lib/mqttable/mqtt_packet_processor.ex)

### 新增函数

1. **format_payload_with_reason_code/3**: 增强的payload格式化函数
   - 检查消息类型是否为ACK类型
   - 如果payload为空，使用reason code信息填充

2. **format_reason_code_as_payload/2**: 将reason code转换为描述性文本
   - 支持不同ACK类型的特定reason code映射
   - 提供用户友好的错误描述

3. **calculate_actual_payload_size/2**: 计算实际payload大小
   - 对于ACK消息，返回原始payload的实际大小（通常为0）
   - 不返回格式化后reason code文本的大小

### Reason Code映射示例

#### PUBACK/PUBREC/PUBREL/PUBCOMP
- `0` → "Success"
- `16` → "No matching subscribers"
- `128` → "Unspecified error"
- `135` → "Not authorized"
- 等等...

#### CONNACK
- `0` → "Connection Accepted"
- `128` → "Unspecified error"
- `135` → "Not authorized"
- 等等...

#### SUBACK/UNSUBACK
- `[0, 0, 0]` → "All Subscriptions Successful"
- `[0, 128, 0]` → "2/3 Subscriptions Successful"
- `[135]` → "Subscription Failed: Not authorized"
- `[128, 135]` → "Subscription Failed: Unspecified error"

## 前端实现 (assets/js/app.js)

### 新增功能

1. **getRowMetadata()**: 行元数据提供器
   - 为失败的ACK消息添加CSS类 `failed-ack-row`

2. **isFailedAckMessage()**: 失败ACK检测函数
   - 根据消息类型和reason code判断是否为失败的ACK
   - 支持不同ACK类型的特定失败条件

3. **payloadFormatter()**: 增强的payload格式化器
   - 对ACK消息的reason code信息不进行截断
   - 保持完整的错误描述显示

### 失败判断逻辑

- **CONNACK**: reason_code ≠ 0 为失败
- **SUBACK/UNSUBACK**: reason_code数组中任何非0值为失败
- **DISCONNECT**: reason_code ≠ 0 且 ≠ 1 为失败
- **其他ACK**: reason_code ≠ 0 为失败

## CSS样式 (assets/css/trace.css)

### 新增样式

```css
/* 失败ACK消息高亮 */
.trace-slick-grid .slick-row.failed-ack-row {
  background-color: rgba(239, 68, 68, 0.12) !important;
  border-left: 4px solid rgb(239, 68, 68) !important;
  animation: failed-ack-pulse 2s ease-in-out infinite;
}

/* 失败ACK脉冲动画 */
@keyframes failed-ack-pulse {
  0%, 100% {
    background-color: rgba(239, 68, 68, 0.12);
    border-left-color: rgb(239, 68, 68);
  }
  50% {
    background-color: rgba(239, 68, 68, 0.18);
    border-left-color: rgb(220, 38, 38);
  }
}
```

## 使用效果

### 成功的ACK消息
- Payload列显示: "Success" 或 "Connection Accepted" 等
- Payload/Total列显示: "0B/XKB" (payload_size为0，data_size为实际传输大小)
- 正常行样式，无特殊高亮

### 失败的ACK消息
- Payload列显示: "Unspecified error" 或 "Not authorized" 等
- Payload/Total列显示: "0B/XKB" (payload_size为0，data_size为实际传输大小)
- 整行红色高亮，带有脉冲动画效果
- 左边框加粗显示

### 示例场景

1. **PUBACK成功**: payload显示 "Success"，Payload/Total显示 "0B/XKB"
2. **PUBACK失败**: payload显示 "No matching subscribers"，Payload/Total显示 "0B/XKB"，整行红色高亮
3. **CONNACK失败**: payload显示 "Not authorized"，Payload/Total显示 "0B/XKB"，整行红色高亮
4. **SUBACK部分失败**: payload显示 "2/3 Subscriptions Successful"，Payload/Total显示 "0B/XKB"，整行红色高亮
5. **SUBACK全部失败**: payload显示 "Subscription Failed: Not authorized"，Payload/Total显示 "0B/XKB"，整行红色高亮

## 测试验证

功能已通过以下测试验证：
- 后端payload格式化逻辑测试 ✅
- 前端失败ACK检测逻辑测试 ✅
- 各种ACK类型和reason code组合测试 ✅

## 兼容性

- 保持与现有功能的完全兼容
- 不影响非ACK消息的显示
- 如果ACK消息已有payload，优先显示原始payload
- 支持MQTT 3.1.1和MQTT 5.0协议
