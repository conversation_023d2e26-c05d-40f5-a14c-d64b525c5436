---
description: 
globs: *ex,*heex
alwaysApply: false
---
1. Server Management: Do NOT attempt to start or run the application server using the command mix phx.server. The server is already running externally in a separate process and is managed independently. Avoid any actions that would try to launch a new server instance.
2. Server Status: If you need to check the status, health, or obtain information about the currently running server process, use the available mcp tools. This is the designated method for querying server state.
UI Components:
3. All user interface (UI) components must be sourced exclusively from DaisyUI components as documented at: https://daisyui.com/components/.
You are explicitly instructed NOT to create custom UI components from scratch. Reuse existing DaisyUI components whenever possible.
4. For specific details on how to integrate or use these components within the project's context, access relevant documentation or context via context7.
5. For specific details on how to use phoenix liveView access relevant documentation or context via context7.