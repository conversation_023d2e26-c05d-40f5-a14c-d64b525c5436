# Enhanced Template Helper - 使用指南

## 🎉 实现完成

已成功实现**方案 3 + 方案 2 组合**：分层显示 + 搜索过滤，并去掉了 `fake_` 前缀！

## 🚀 如何使用

### 1. 访问演示页面
```bash
# 启动应用
mix phx.server

# 在浏览器中访问
http://localhost:4000/demo/enhanced-editor
```

### 2. 在现有项目中使用

#### 替换现有的 PayloadEditorComponent
```elixir
# 旧的组件
<.live_component
  module={MqttableWeb.PayloadEditorComponent}
  id="payload-editor"
  payload={@payload}
  payload_format={@payload_format}
/>

# 新的增强版组件
<.live_component
  module={MqttableWeb.EnhancedPayloadEditorComponent}
  id="enhanced-payload-editor"
  payload={@payload}
  payload_format={@payload_format}
  label="Template Payload"
/>
```

## ✨ 新功能使用方法

### 🔍 搜索功能
1. 点击 "Template Helper" 按钮打开助手面板
2. 在搜索框中输入关键词（如 "name", "temperature", "city"）
3. 实时过滤显示匹配的函数

### 📂 分类过滤
1. 使用分类下拉菜单选择特定类别：
   - **IoT & Sensors** - 传感器数据
   - **Person Data** - 人员信息
   - **Address & Location** - 地址位置
   - **Company & Business** - 公司商务
   - **Internet & Tech** - 网络技术
   - **Commerce & Products** - 商务产品
   - **Text & Content** - 文本内容
   - **Other** - 其他工具

### 📋 分层显示
- **默认状态**：显示12个最常用函数
- **展开状态**：点击 "Show More (XX)" 显示所有函数
- **自动展开**：搜索或选择分类时自动展开

### ✨ 简洁函数名
现在可以使用更简洁的函数名：

```liquid
<!-- 新的简洁语法 -->
{
  "user": "{{ name }}",           // ✅ 简洁
  "email": "{{ email }}",         // ✅ 简洁
  "company": "{{ company }}",     // ✅ 简洁
  "location": "{{ city }}, {{ country }}"
}

<!-- 旧的语法仍然支持 -->
{
  "user": "{{ fake_name }}",      // ✅ 向后兼容
  "email": "{{ fake_email }}",    // ✅ 向后兼容
  "company": "{{ fake_company }}" // ✅ 向后兼容
}
```

## 📊 可用函数列表

### IoT & 传感器数据
```liquid
{{ temperature }}     // 温度
{{ humidity }}        // 湿度
{{ pressure }}        // 压力
{{ battery_level }}   // 电池电量
{{ device_id }}       // 设备ID
{{ device_status }}   // 设备状态
{{ signal_strength }} // 信号强度

```

### 人员数据
```liquid
{{ name }}            // 姓名
{{ first_name }}      // 名字
{{ last_name }}       // 姓氏
{{ title }}           // 职位
{{ email }}           // 邮箱
{{ username }}        // 用户名
```

### 地址位置
```liquid
{{ address }}         // 地址
{{ city }}            // 城市
{{ country }}         // 国家
{{ state }}           // 州/省
{{ postcode }}        // 邮编
{{ latitude }}        // 纬度
{{ longitude }}       // 经度
{{ timezone }}        // 时区
```

### 公司商务
```liquid
{{ company }}         // 公司名
{{ buzzword }}        // 商业术语
{{ catch_phrase }}    // 宣传语
{{ department }}      // 部门
```

### 网络技术
```liquid
{{ ipv4 }}            // IPv4地址
{{ ipv6 }}            // IPv6地址
{{ domain }}          // 域名
{{ url }}             // URL
{{ mac_address }}     // MAC地址
```

### 商务产品
```liquid
{{ product_name }}    // 产品名
{{ price }}           // 价格
{{ color_name }}      // 颜色
{{ vehicle }}         // 车辆
{{ dish }}            // 菜品
```

### 文本内容
```liquid
{{ sentence }}        // 句子
{{ paragraph }}       // 段落
{{ word }}            // 单词
```

### 其他工具
```liquid
{{ uuid }}            // UUID
{{ iso8601 }}         // 时间戳
{{ random_int }}      // 随机数
{{ currency_code }}   // 货币代码
{{ bitcoin_address }} // 比特币地址
```

## 🎯 实际使用示例

### IoT 设备数据
```json
{
  "device_id": "{{ device_id }}",
  "timestamp": "{{ iso8601 }}",
  "location": {
    "city": "{{ city }}",
    "country": "{{ country }}",
    "coordinates": {
      "lat": {{ latitude }},
      "lng": {{ longitude }}
    }
  },
  "sensors": {
    "temperature": {{ temperature }},
    "humidity": {{ humidity }},
    "pressure": {{ pressure }},
    "battery": {{ battery_level }}
  },
  "status": "{{ device_status }}"
}
```

### 用户资料
```json
{
  "profile": {
    "name": "{{ name }}",
    "email": "{{ email }}",
    "title": "{{ title }}",
    "company": "{{ company }}"
  },
  "address": {
    "street": "{{ address }}",
    "city": "{{ city }}",
    "state": "{{ state }}",
    "country": "{{ country }}",
    "postcode": "{{ postcode }}"
  },
  "contact": {
    "ip": "{{ ipv4 }}",
    "domain": "{{ domain }}"
  }
}
```

### 商品信息
```liquid
Product: {{ product_name }}
Price: ${{ price }}
Color: {{ color_name }}
Department: {{ department }}
Description: {{ sentence }}

Vehicle Information:
- Make/Model: {{ vehicle }}
- VIN: {{ vin }}

Food Menu:
- Today's Special: {{ dish }}
- Main Ingredient: {{ ingredient }}
```

## 🔧 技术细节

### 函数总数
- **总函数数量**：160个
- **原始函数**：105个
- **简洁别名**：55个
- **UI显示**：46个主要函数

### 性能优化
- 常用函数预计算
- 按需渲染
- 智能过滤
- 分层加载

### 兼容性
- 完全向后兼容
- 支持新旧语法
- 无需修改现有模板

## 🎉 总结

这个增强版模板助手完美解决了105个函数的展示问题，提供了：

1. **更好的用户体验** - 搜索、分类、分层显示
2. **更简洁的语法** - 去掉 `fake_` 前缀
3. **更强的功能** - 实时预览、错误提示
4. **更好的性能** - 智能加载、按需渲染

现在用户可以轻松管理和使用所有105+个模板函数！🚀
