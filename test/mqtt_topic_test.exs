defmodule Mqttable.MqttClient.TopicTest do
  use ExUnit.Case

  describe "match/2" do
    test "exact topic matching" do
      assert Mqttable.MqttClient.Topic.match("a/b/c", "a/b/c") == true
      assert Mqttable.MqttClient.Topic.match("a/b/c", "a/b/d") == false
      assert Mqttable.MqttClient.Topic.match("topic", "topic") == true
      assert Mqttable.MqttClient.Topic.match("", "") == true
    end

    test "single level wildcard matching" do
      assert Mqttable.MqttClient.Topic.match("a/b/c", "a/+/c") == true
      assert Mqttable.MqttClient.Topic.match("a/b/c", "+/b/c") == true
      assert Mqttable.MqttClient.Topic.match("a/b/c", "a/b/+") == true
      assert Mqttable.MqttClient.Topic.match("a/b/c", "+/+/+") == true
      assert Mqttable.MqttClient.Topic.match("a/b", "a/+/c") == false
      assert Mqttable.MqttClient.Topic.match("a/b/c/d", "a/+/c") == false
    end

    test "multi-level wildcard matching" do
      assert Mqttable.MqttClient.Topic.match("a/b/c", "a/b/#") == true
      assert Mqttable.MqttClient.Topic.match("a/b/c/d/e", "a/b/#") == true
      assert Mqttable.MqttClient.Topic.match("a/b", "a/b/#") == true
      assert Mqttable.MqttClient.Topic.match("a/b/c", "a/#") == true
      assert Mqttable.MqttClient.Topic.match("a", "a/#") == true
      assert Mqttable.MqttClient.Topic.match("", "#") == true
      assert Mqttable.MqttClient.Topic.match("a/b/c", "#") == true
      assert Mqttable.MqttClient.Topic.match("a/b/c", "b/#") == false
    end

    test "system topic matching" do
      # System topics don't match wildcards
      assert Mqttable.MqttClient.Topic.match("$SYS/broker/stats", "+/broker/stats") == false
      assert Mqttable.MqttClient.Topic.match("$SYS/broker/stats", "#") == false
      assert Mqttable.MqttClient.Topic.match("$SYS/broker/stats", "$SYS/broker/stats") == true
      assert Mqttable.MqttClient.Topic.match("$SYS/broker/stats", "$SYS/+/stats") == true
      assert Mqttable.MqttClient.Topic.match("$SYS/broker/stats", "$SYS/#") == true
    end

    test "empty level matching" do
      assert Mqttable.MqttClient.Topic.match("a//c", "a/+/c") == true
      assert Mqttable.MqttClient.Topic.match("a//c", "a//c") == true
      assert Mqttable.MqttClient.Topic.match("/a/b", "+/a/b") == true
      assert Mqttable.MqttClient.Topic.match("a/b/", "a/b/+") == true
    end

    test "shared subscription matching" do
      share1 = %{group: "group1", topic: "a/b/c"}
      share2 = %{group: "group1", topic: "a/+/c"}
      share3 = %{group: "group2", topic: "a/b/c"}

      assert Mqttable.MqttClient.Topic.match(share1, share2) == true
      assert Mqttable.MqttClient.Topic.match(share1, share3) == false
      assert Mqttable.MqttClient.Topic.match(share1, "a/b/c") == true
      assert Mqttable.MqttClient.Topic.match("a/b/c", %{topic: "a/b/c"}) == true
    end

    test "words list matching" do
      assert Mqttable.MqttClient.Topic.match(["a", "b", "c"], ["a", "b", "c"]) == true
      assert Mqttable.MqttClient.Topic.match(["a", "b", "c"], ["a", :+, "c"]) == true
      assert Mqttable.MqttClient.Topic.match(["a", "b", "c"], ["a", :"#"]) == true
      assert Mqttable.MqttClient.Topic.match(["a", "b", "c"], [:"#"]) == true
    end
  end

  describe "match_any/2" do
    test "matches any filter in list" do
      filters = ["a/b/c", "x/y/z", "m/+/n"]

      assert Mqttable.MqttClient.Topic.match_any("a/b/c", filters) == true
      assert Mqttable.MqttClient.Topic.match_any("x/y/z", filters) == true
      assert Mqttable.MqttClient.Topic.match_any("m/test/n", filters) == true
      assert Mqttable.MqttClient.Topic.match_any("no/match", filters) == false
    end

    test "empty filter list" do
      assert Mqttable.MqttClient.Topic.match_any("topic", []) == false
    end

    test "single filter" do
      assert Mqttable.MqttClient.Topic.match_any("a/b/c", ["a/+/c"]) == true
      assert Mqttable.MqttClient.Topic.match_any("a/b/c", ["x/y/z"]) == false
    end
  end

  describe "validate/1 and validate/2" do
    test "valid topic names" do
      assert Mqttable.MqttClient.Topic.validate({:name, "a/b/c"}) == true
      assert Mqttable.MqttClient.Topic.validate({:name, "topic"}) == true
      assert Mqttable.MqttClient.Topic.validate({:name, "a/b/"}) == true
      assert Mqttable.MqttClient.Topic.validate({:name, "/a/b"}) == true
      assert Mqttable.MqttClient.Topic.validate({:name, "测试/topic"}) == true
    end

    test "invalid topic names" do
      assert_raise RuntimeError, "topic_name_error", fn ->
        Mqttable.MqttClient.Topic.validate({:name, "a/+/c"})
      end

      assert_raise RuntimeError, "topic_name_error", fn ->
        Mqttable.MqttClient.Topic.validate({:name, "a/#"})
      end
    end

    test "valid topic filters" do
      assert Mqttable.MqttClient.Topic.validate({:filter, "a/b/c"}) == true
      assert Mqttable.MqttClient.Topic.validate({:filter, "a/+/c"}) == true
      assert Mqttable.MqttClient.Topic.validate({:filter, "a/b/#"}) == true
      assert Mqttable.MqttClient.Topic.validate({:filter, "#"}) == true
      assert Mqttable.MqttClient.Topic.validate({:filter, "+"}) == true
      assert Mqttable.MqttClient.Topic.validate({:filter, "+/+/+"}) == true
    end

    test "invalid topic filters" do
      assert_raise RuntimeError, "topic_invalid_#", fn ->
        Mqttable.MqttClient.Topic.validate({:filter, "a/#/c"})
      end

      assert_raise RuntimeError, "topic_invalid_char", fn ->
        Mqttable.MqttClient.Topic.validate({:filter, "a/b+/c"})
      end

      assert_raise RuntimeError, "topic_invalid_char", fn ->
        Mqttable.MqttClient.Topic.validate({:filter, "a/b#/c"})
      end
    end

    test "empty topic" do
      assert_raise RuntimeError, "empty_topic", fn ->
        Mqttable.MqttClient.Topic.validate("")
      end
    end

    test "topic too long" do
      long_topic = String.duplicate("a", 65536)

      assert_raise RuntimeError, "topic_too_long", fn ->
        Mqttable.MqttClient.Topic.validate(long_topic)
      end
    end

    test "shared subscription validation" do
      assert Mqttable.MqttClient.Topic.validate({:filter, "$share/group/topic"}) == true
      assert Mqttable.MqttClient.Topic.validate({:filter, "$share/group/a/+/c"}) == true
      assert Mqttable.MqttClient.Topic.validate({:filter, "$share/group/a/#"}) == true

      assert_raise RuntimeError, "share_empty_group", fn ->
        Mqttable.MqttClient.Topic.validate({:filter, "$share//topic"})
      end

      assert_raise RuntimeError, "share_empty_filter", fn ->
        Mqttable.MqttClient.Topic.validate({:filter, "$share/group/"})
      end

      assert_raise RuntimeError, "share_name_invalid_char", fn ->
        Mqttable.MqttClient.Topic.validate({:filter, "$share/group+/topic"})
      end

      assert_raise RuntimeError, "share_recursively", fn ->
        Mqttable.MqttClient.Topic.validate({:filter, "$share/group/$share/inner/topic"})
      end
    end

    test "default validation type" do
      assert Mqttable.MqttClient.Topic.validate("a/+/c") == true
      assert Mqttable.MqttClient.Topic.validate("a/#") == true
    end
  end

  describe "parse/1 and parse/2" do
    test "simple topic parsing" do
      assert Mqttable.MqttClient.Topic.parse("a/b/c") == {"a/b/c", %{}}
      assert Mqttable.MqttClient.Topic.parse("topic") == {"topic", %{}}
      assert Mqttable.MqttClient.Topic.parse({"a/b/c", %{qos: 1}}) == {"a/b/c", %{qos: 1}}
    end

    test "queue topic parsing" do
      expected = %{group: "$queue", topic: "a/b/c"}
      assert Mqttable.MqttClient.Topic.parse("$queue/a/b/c") == {expected, %{}}

      assert Mqttable.MqttClient.Topic.parse("$queue/a/b/c", %{qos: 1}) == {expected, %{qos: 1}}
    end

    test "shared subscription parsing" do
      expected = %{group: "mygroup", topic: "a/b/c"}
      assert Mqttable.MqttClient.Topic.parse("$share/mygroup/a/b/c") == {expected, %{}}

      expected_queue = %{group: "$queue", topic: "topic"}
      assert Mqttable.MqttClient.Topic.parse("$share/$queue/topic") == {expected_queue, %{}}
    end

    test "exclusive subscription parsing" do
      assert Mqttable.MqttClient.Topic.parse("$exclusive/topic") ==
               {"topic", %{is_exclusive: true}}

      assert Mqttable.MqttClient.Topic.parse("$exclusive/a/b/c", %{qos: 1}) ==
               {"a/b/c", %{qos: 1, is_exclusive: true}}
    end

    test "invalid shared subscription parsing" do
      assert_raise RuntimeError, fn ->
        Mqttable.MqttClient.Topic.parse("$share/group")
      end

      assert_raise RuntimeError, fn ->
        Mqttable.MqttClient.Topic.parse("$share/group+/topic")
      end

      assert_raise RuntimeError, fn ->
        Mqttable.MqttClient.Topic.parse("$exclusive/")
      end
    end

    test "invalid nested shared topics" do
      share_with_queue = %{topic: "$queue/topic"}

      assert_raise RuntimeError, fn ->
        Mqttable.MqttClient.Topic.parse(share_with_queue)
      end

      share_with_share = %{topic: "$share/group/topic"}

      assert_raise RuntimeError, fn ->
        Mqttable.MqttClient.Topic.parse(share_with_share)
      end
    end

    test "no local option validation" do
      share = %{group: "group", topic: "topic"}

      assert_raise RuntimeError, fn ->
        Mqttable.MqttClient.Topic.parse(share, %{nl: 1})
      end
    end
  end

  describe "wildcard/1" do
    test "detects wildcards in topics" do
      assert Mqttable.MqttClient.Topic.wildcard("a/+/c") == true
      assert Mqttable.MqttClient.Topic.wildcard("a/b/#") == true
      assert Mqttable.MqttClient.Topic.wildcard("+") == true
      assert Mqttable.MqttClient.Topic.wildcard("#") == true
      assert Mqttable.MqttClient.Topic.wildcard("a/b/c") == false
      assert Mqttable.MqttClient.Topic.wildcard("") == false
    end

    test "detects wildcards in words" do
      assert Mqttable.MqttClient.Topic.wildcard(["a", :+, "c"]) == true
      assert Mqttable.MqttClient.Topic.wildcard(["a", "b", :"#"]) == true
      assert Mqttable.MqttClient.Topic.wildcard([:"#"]) == true
      assert Mqttable.MqttClient.Topic.wildcard(["a", "b", "c"]) == false
      assert Mqttable.MqttClient.Topic.wildcard([]) == false
    end

    test "detects wildcards in shared subscriptions" do
      share_with_wildcard = %{topic: "a/+/c"}
      assert Mqttable.MqttClient.Topic.wildcard(share_with_wildcard) == true

      share_without_wildcard = %{topic: "a/b/c"}
      assert Mqttable.MqttClient.Topic.wildcard(share_without_wildcard) == false
    end
  end

  describe "words/1" do
    test "splits topic into words" do
      assert Mqttable.MqttClient.Topic.words("a/b/c") == ["a", "b", "c"]
      assert Mqttable.MqttClient.Topic.words("a/+/c") == ["a", :+, "c"]
      assert Mqttable.MqttClient.Topic.words("a/b/#") == ["a", "b", :"#"]
      assert Mqttable.MqttClient.Topic.words("") == [:""]
      assert Mqttable.MqttClient.Topic.words("/") == [:"", :""]
      assert Mqttable.MqttClient.Topic.words("a//c") == ["a", :"", "c"]
    end

    test "splits shared subscription topics" do
      share = %{topic: "a/b/c"}
      assert Mqttable.MqttClient.Topic.words(share) == ["a", "b", "c"]
    end
  end

  describe "join/1" do
    test "joins words into topic" do
      assert Mqttable.MqttClient.Topic.join(["a", "b", "c"]) == "a/b/c"
      assert Mqttable.MqttClient.Topic.join(["a", :+, "c"]) == "a/+/c"
      assert Mqttable.MqttClient.Topic.join(["a", "b", :"#"]) == "a/b/#"
      assert Mqttable.MqttClient.Topic.join([]) == ""
      assert Mqttable.MqttClient.Topic.join([:""]) == ""
      assert Mqttable.MqttClient.Topic.join([:"", :""]) == "/"
    end

    test "validates multi-level wildcard position" do
      assert_raise RuntimeError, "topic_invalid_#", fn ->
        Mqttable.MqttClient.Topic.join(["a", :"#", "c"])
      end
    end
  end

  describe "integration tests" do
    test "topic matching workflow" do
      # Create a topic filter
      topic_filter = "sensors/+/temperature"

      # Validate it
      assert Mqttable.MqttClient.Topic.validate({:filter, topic_filter}) == true

      # Parse it
      {parsed_filter, _options} = Mqttable.MqttClient.Topic.parse(topic_filter)

      # Test matching
      assert Mqttable.MqttClient.Topic.match("sensors/room1/temperature", parsed_filter) == true
      assert Mqttable.MqttClient.Topic.match("sensors/room2/temperature", parsed_filter) == true
      assert Mqttable.MqttClient.Topic.match("sensors/room1/humidity", parsed_filter) == false

      # Test with multiple filters
      filters = [topic_filter, "sensors/+/humidity", "alerts/#"]
      assert Mqttable.MqttClient.Topic.match_any("sensors/room1/temperature", filters) == true
      assert Mqttable.MqttClient.Topic.match_any("sensors/room1/humidity", filters) == true
      assert Mqttable.MqttClient.Topic.match_any("alerts/fire", filters) == true
      assert Mqttable.MqttClient.Topic.match_any("other/topic", filters) == false
    end

    test "shared subscription workflow" do
      # Parse shared subscription
      {parsed, _options} = Mqttable.MqttClient.Topic.parse("$share/workers/jobs/+")

      # Should be a share record
      assert is_map(parsed)
      assert parsed.group == "workers"
      assert parsed.topic == "jobs/+"

      # Test matching
      assert Mqttable.MqttClient.Topic.match(%{group: "workers", topic: "jobs/task1"}, parsed) ==
               true

      assert Mqttable.MqttClient.Topic.match(%{group: "workers", topic: "jobs/task2"}, parsed) ==
               true

      assert Mqttable.MqttClient.Topic.match(%{group: "other", topic: "jobs/task1"}, parsed) ==
               false
    end

    test "complex topic validation" do
      complex_topics = [
        "building/floor1/room101/sensors/temperature",
        "building/floor2/+/sensors/+",
        "building/+/+/alerts/#",
        # Unicode support
        "建筑物/楼层1/房间/传感器",
        "$SYS/brokers/node1/stats"
      ]

      Enum.each(complex_topics, fn topic ->
        assert Mqttable.MqttClient.Topic.validate({:filter, topic}) == true
      end)

      # Test invalid topics
      invalid_topics = [
        # # not at end
        "building/floor#/room",
        # + with other chars
        "building/floo+r/room",
        # # with trailing chars
        "building/floor/room#extra"
      ]

      Enum.each(invalid_topics, fn topic ->
        assert_raise RuntimeError, fn ->
          Mqttable.MqttClient.Topic.validate({:filter, topic})
        end
      end)
    end
  end
end
