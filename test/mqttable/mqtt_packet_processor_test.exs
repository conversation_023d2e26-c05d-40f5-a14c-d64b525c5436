defmodule Mqttable.MqttPacketProcessorTest do
  use ExUnit.Case, async: true

  # Test the private format_reason_code_as_payload function through public interface
  # by creating a test module that can access the private functions
  defmodule TestHelper do
    # Replicate the logic from format_reason_code_as_payload for testing
    def format_suback_payload(reason_codes) when is_list(reason_codes) do
      success_count = Enum.count(reason_codes, &(&1 == 0))
      total_count = length(reason_codes)

      cond do
        success_count == total_count ->
          # All successful
          "All Subscriptions Successful"

        success_count == 0 ->
          # All failed - show specific error message
          error_message = format_subscription_error(reason_codes)
          "Subscription Failed: #{error_message}"

        true ->
          # Partial success
          "#{success_count}/#{total_count} Subscriptions Successful"
      end
    end

    def format_unsuback_payload(reason_codes) when is_list(reason_codes) do
      success_count = Enum.count(reason_codes, &(&1 == 0))
      total_count = length(reason_codes)

      cond do
        success_count == total_count ->
          # All successful
          "All Unsubscriptions Successful"

        success_count == 0 ->
          # All failed - show specific error message
          error_message = format_unsubscription_error(reason_codes)
          "Unsubscription Failed: #{error_message}"

        true ->
          # Partial success
          "#{success_count}/#{total_count} Unsubscriptions Successful"
      end
    end

    # Format subscription error message based on reason codes
    defp format_subscription_error(reason_codes) when is_list(reason_codes) do
      # Get the first error reason code (most relevant)
      first_error = Enum.find(reason_codes, &(&1 != 0))
      format_single_reason_code(first_error || 0)
    end

    # Format unsubscription error message based on reason codes
    defp format_unsubscription_error(reason_codes) when is_list(reason_codes) do
      # Get the first error reason code (most relevant)
      first_error = Enum.find(reason_codes, &(&1 != 0))
      format_single_reason_code(first_error || 0)
    end

    # Format a single reason code to human-readable message
    defp format_single_reason_code(reason_code) do
      case reason_code do
        0 -> "Success"
        128 -> "Unspecified error"
        129 -> "Implementation specific error"
        131 -> "Implementation specific error"
        135 -> "Not authorized"
        143 -> "Topic filter invalid"
        145 -> "Packet identifier in use"
        151 -> "Quota exceeded"
        158 -> "Shared subscriptions not supported"
        161 -> "Subscription identifiers not supported"
        162 -> "Wildcard subscriptions not supported"
        _ -> "Error (#{reason_code})"
      end
    end
  end

  describe "SUBACK payload formatting" do
    test "all subscriptions successful" do
      reason_codes = [0, 0, 0]
      result = TestHelper.format_suback_payload(reason_codes)
      assert result == "All Subscriptions Successful"
    end

    test "all subscriptions failed with not authorized" do
      reason_codes = [135]
      result = TestHelper.format_suback_payload(reason_codes)
      assert result == "Subscription Failed: Not authorized"
    end

    test "all subscriptions failed with unspecified error" do
      reason_codes = [128, 128]
      result = TestHelper.format_suback_payload(reason_codes)
      assert result == "Subscription Failed: Unspecified error"
    end

    test "partial subscription success" do
      reason_codes = [0, 135, 0]
      result = TestHelper.format_suback_payload(reason_codes)
      assert result == "2/3 Subscriptions Successful"
    end

    test "single subscription failed" do
      reason_codes = [135]
      result = TestHelper.format_suback_payload(reason_codes)
      assert result == "Subscription Failed: Not authorized"
    end

    test "multiple different errors shows first error" do
      reason_codes = [135, 128, 143]
      result = TestHelper.format_suback_payload(reason_codes)
      assert result == "Subscription Failed: Not authorized"
    end
  end

  describe "UNSUBACK payload formatting" do
    test "all unsubscriptions successful" do
      reason_codes = [0, 0]
      result = TestHelper.format_unsuback_payload(reason_codes)
      assert result == "All Unsubscriptions Successful"
    end

    test "all unsubscriptions failed" do
      reason_codes = [135]
      result = TestHelper.format_unsuback_payload(reason_codes)
      assert result == "Unsubscription Failed: Not authorized"
    end

    test "partial unsubscription success" do
      reason_codes = [0, 128]
      result = TestHelper.format_unsuback_payload(reason_codes)
      assert result == "1/2 Unsubscriptions Successful"
    end
  end

  describe "reason code error messages" do
    test "maps common MQTT error reason codes correctly" do
      # Only test error codes (non-zero), since 0 means success
      test_cases = [
        {128, "Unspecified error"},
        {135, "Not authorized"},
        {143, "Topic filter invalid"},
        {151, "Quota exceeded"},
        {999, "Error (999)"}
      ]

      for {code, expected} <- test_cases do
        reason_codes = [code]
        result = TestHelper.format_suback_payload(reason_codes)
        assert result == "Subscription Failed: #{expected}"
      end
    end

    test "success reason code shows success message" do
      reason_codes = [0]
      result = TestHelper.format_suback_payload(reason_codes)
      assert result == "All Subscriptions Successful"
    end
  end

  describe "payload size calculation for ACK messages" do
    defmodule PayloadSizeHelper do
      def calculate_actual_payload_size(payload, packet_type) do
        ack_types = [
          "PUBACK",
          "PUBREC",
          "PUBREL",
          "PUBCOMP",
          "SUBACK",
          "UNSUBACK",
          "CONNACK",
          "DISCONNECT"
        ]

        if packet_type in ack_types do
          # For ACK messages, return the actual payload size (usually 0)
          case payload do
            payload when is_binary(payload) -> byte_size(payload)
            nil -> 0
            _ -> 0
          end
        else
          # For non-ACK messages, calculate normal payload size
          case payload do
            payload when is_binary(payload) -> byte_size(payload)
            nil -> 0
            _ -> 0
          end
        end
      end
    end

    test "ACK messages with nil payload should have payload_size 0" do
      ack_types = [
        "PUBACK",
        "PUBREC",
        "PUBREL",
        "PUBCOMP",
        "SUBACK",
        "UNSUBACK",
        "CONNACK",
        "DISCONNECT"
      ]

      for packet_type <- ack_types do
        payload_size = PayloadSizeHelper.calculate_actual_payload_size(nil, packet_type)
        assert payload_size == 0, "#{packet_type} with nil payload should have payload_size 0"
      end
    end

    test "ACK messages with empty binary payload should have payload_size 0" do
      ack_types = [
        "PUBACK",
        "PUBREC",
        "PUBREL",
        "PUBCOMP",
        "SUBACK",
        "UNSUBACK",
        "CONNACK",
        "DISCONNECT"
      ]

      for packet_type <- ack_types do
        payload_size = PayloadSizeHelper.calculate_actual_payload_size("", packet_type)
        assert payload_size == 0, "#{packet_type} with empty payload should have payload_size 0"
      end
    end

    test "non-ACK messages should calculate normal payload size" do
      payload = "Hello World"
      payload_size = PayloadSizeHelper.calculate_actual_payload_size(payload, "PUBLISH")
      assert payload_size == 11
    end

    test "ACK messages with actual payload should show actual size" do
      # Some ACK messages might have actual payload in certain cases
      payload = "test"
      payload_size = PayloadSizeHelper.calculate_actual_payload_size(payload, "SUBACK")
      assert payload_size == 4
    end
  end
end
