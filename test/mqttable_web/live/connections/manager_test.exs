defmodule MqttableWeb.Connections.ManagerTest do
  use ExUnit.Case, async: true

  describe "uniqueness validation logic" do
    test "validates unique name and client_id" do
      existing_connections = [
        %{name: "existing_connection", client_id: "existing_client_123"},
        %{name: "another_connection", client_id: "another_client_456"}
      ]

      # Test duplicate name
      assert has_duplicate_name?(existing_connections, "existing_connection")
      refute has_duplicate_name?(existing_connections, "unique_name")

      # Test duplicate client_id
      assert has_duplicate_client_id?(existing_connections, "existing_client_123")
      refute has_duplicate_client_id?(existing_connections, "unique_client_id")
    end

    test "validates uniqueness excluding current connection" do
      existing_connections = [
        %{name: "existing_connection", client_id: "existing_client_123"},
        %{name: "another_connection", client_id: "another_client_456"}
      ]

      # Exclude the connection being edited
      filtered_connections =
        Enum.reject(existing_connections, fn conn ->
          conn.client_id == "existing_client_123"
        end)

      # Should allow keeping the same name when editing
      refute has_duplicate_name?(filtered_connections, "existing_connection")
      # But should still reject other existing names
      assert has_duplicate_name?(filtered_connections, "another_connection")

      # Should allow keeping the same client_id when editing
      refute has_duplicate_client_id?(filtered_connections, "existing_client_123")
      # But should still reject other existing client_ids
      assert has_duplicate_client_id?(filtered_connections, "another_client_456")
    end
  end

  # Helper functions to test the validation logic
  defp has_duplicate_name?(connections, name) do
    Enum.any?(connections, fn conn -> conn.name == name end)
  end

  defp has_duplicate_client_id?(connections, client_id) do
    Enum.any?(connections, fn conn -> conn.client_id == client_id end)
  end
end
