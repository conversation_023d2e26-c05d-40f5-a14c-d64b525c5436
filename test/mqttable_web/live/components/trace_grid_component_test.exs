defmodule MqttableWeb.TraceGridComponentTest do
  use ExUnit.Case, async: true

  # Test the ID generation function directly
  # We'll copy the function here to test it in isolation
  defp generate_grid_id(broker_name) do
    sanitized =
      case broker_name do
        nil ->
          "default"

        "" ->
          "default"

        name ->
          name
          |> String.downcase()
          |> String.replace(~r/[^a-z0-9\-_]/, "-")
          |> String.replace(~r/-+/, "-")
          |> String.trim("-")
          # Limit length to prevent overly long IDs
          |> String.slice(0, 50)
      end

    "trace-slick-grid-#{sanitized}"
  end

  describe "generate_grid_id/1" do
    test "generates default ID for nil broker name" do
      assert generate_grid_id(nil) == "trace-slick-grid-default"
    end

    test "generates default ID for empty broker name" do
      assert generate_grid_id("") == "trace-slick-grid-default"
    end

    test "generates ID with sanitized broker name" do
      assert generate_grid_id("My Test Broker") == "trace-slick-grid-my-test-broker"
    end

    test "sanitizes special characters to hyphens" do
      assert generate_grid_id("Broker@123#Test$") == "trace-slick-grid-broker-123-test"
    end

    test "handles multiple consecutive special characters" do
      assert generate_grid_id("Test@@@Broker###") == "trace-slick-grid-test-broker"
    end

    test "preserves valid characters" do
      assert generate_grid_id("broker-123_test") == "trace-slick-grid-broker-123_test"
    end

    test "truncates very long broker names" do
      long_name = String.duplicate("very-long-broker-name-", 10)
      result = generate_grid_id(long_name)

      # Should start with prefix and be truncated
      assert String.starts_with?(result, "trace-slick-grid-")
      assert String.length(result) <= String.length("trace-slick-grid-") + 50
    end

    test "handles uppercase letters" do
      assert generate_grid_id("UPPERCASE_BROKER") == "trace-slick-grid-uppercase_broker"
    end

    test "handles mixed case with spaces and numbers" do
      assert generate_grid_id("Broker With Spaces and 123 Numbers") ==
               "trace-slick-grid-broker-with-spaces-and-123-numbers"
    end

    test "trims leading and trailing hyphens" do
      assert generate_grid_id("@@@broker@@@") == "trace-slick-grid-broker"
    end
  end
end
