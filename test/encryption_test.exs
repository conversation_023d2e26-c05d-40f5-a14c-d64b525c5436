defmodule Mqttable.EncryptionTest do
  use ExUnit.Case
  alias Mqttable.Encryption

  test "encrypts and decrypts passwords correctly" do
    original_password = "my_secret_password"
    encrypted = Encryption.encrypt(original_password)
    decrypted = Encryption.decrypt(encrypted)

    assert original_password == decrypted
    assert Encryption.encrypted?(encrypted)
    assert String.starts_with?(encrypted, "encrypted:")
    assert encrypted != original_password
  end

  test "handles empty string" do
    empty = ""
    encrypted_empty = Encryption.encrypt(empty)
    decrypted_empty = Encryption.decrypt(encrypted_empty)

    assert empty == decrypted_empty
    assert encrypted_empty == ""
  end

  test "handles nil values" do
    nil_value = nil
    encrypted_nil = Encryption.encrypt(nil_value)
    decrypted_nil = Encryption.decrypt(encrypted_nil)

    assert nil_value != encrypted_nil
    assert encrypted_nil == ""
    assert decrypted_nil == ""
  end

  test "non-encrypted strings are returned as-is" do
    normal_string = "not_encrypted"
    result = Encryption.decrypt(normal_string)

    assert result == normal_string
  end
end
