# This file is responsible for configuring your application
# and its dependencies with the aid of the Config module.
#
# This configuration file is loaded before any dependency and
# is restricted to this project.

# General application configuration
import Config

config :mqttable,
  generators: [timestamp_type: :utc_datetime]

config :mqttable,
  time_zone: "Asia/Shanghai"

# Configures the endpoint
config :mqttable, MqttableWeb.Endpoint,
  url: [host: "localhost"],
  adapter: Bandit.PhoenixAdapter,
  render_errors: [
    formats: [html: MqttableWeb.ErrorHTML, json: MqttableWeb.ErrorJSON],
    layout: false
  ],
  pubsub_server: Mqttable.PubSub,
  live_view: [signing_salt: "w5FqJm2Z"]

# Configure esbuild (the version is required)
config :esbuild,
  version: "0.17.11",
  mqttable: [
    args:
      ~w(js/app.js --bundle --target=es2022 --outdir=../priv/static/assets/js --external:/fonts/* --external:/images/*),
    cd: Path.expand("../assets", __DIR__),
    env: %{"NODE_PATH" => Path.expand("../deps", __DIR__)}
  ]

# Configure tailwind (the version is required)
config :tailwind,
  version: "4.0.9",
  mqttable: [
    args: ~w(
      --input=assets/css/app.css
      --output=priv/static/assets/css/app.css
    ),
    cd: Path.expand("..", __DIR__)
  ]

# Configures Elixir's Logger
config :logger, :default_formatter,
  format: "$time $metadata[$level] $message\n",
  metadata: [:request_id]

config :elixir, :time_zone_database, Tz.TimeZoneDatabase

# Use Jason for JSON parsing in Phoenix
config :phoenix, :json_library, Jason

# Configure MIME types for certificate files
config :mime, :types, %{
  "application/x-pem-file" => ["pem"],
  "application/x-x509-ca-cert" => ["crt", "cert"],
  "application/x-pkcs12" => ["p12", "pfx"],
  "application/x-pkcs8" => ["key", "der"]
}

# Import environment specific config. This must remain at the bottom
# of this file so it overrides the configuration defined above.
import_config "#{config_env()}.exs"
